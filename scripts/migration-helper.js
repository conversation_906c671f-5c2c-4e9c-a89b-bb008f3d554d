#!/usr/bin/env node

/**
 * Migration Helper Script
 *
 * This script provides utilities for managing database migrations:
 * - Automatically detects model changes and suggests migration steps
 * - Provides migration workflow guidance
 * - Validates migration consistency
 */

import { sequelize } from '../src/config/database.config.js';
import { readdirSync, readFileSync, statSync } from 'fs';
import { join, extname } from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const MIGRATIONS_DIR = join(__dirname, '..', 'migrations');
const MODELS_DIR = join(__dirname, '..', 'models');

// Colors for output
const colors = {
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logStep(message) {
  log(`${colors.bold}${message}${colors.reset}`, 'blue');
}

async function checkDatabaseConnection() {
  try {
    await sequelize.authenticate();
    logSuccess('Database connection established');
    return true;
  } catch (error) {
    logError(`Database connection failed: ${error.message}`);
    return false;
  }
}

async function getMigrationStatus() {
  try {
    const result = await sequelize.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_name = 'sequelize_meta'
    `);

    const hasMetaTable = result[0].length > 0;

    if (hasMetaTable) {
      const migrations = await sequelize.query(`
        SELECT name
        FROM "sequelize_meta"
        ORDER BY name
      `);

      logInfo(`Executed migrations: ${migrations[0].length}`);
      migrations[0].forEach(mig => {
        log(`  - ${mig.name}`, 'green');
      });
    } else {
      logWarning('No migrations table found. Database may not be migrated.');
    }

    return hasMetaTable;
  } catch (error) {
    logError(`Failed to check migration status: ${error.message}`);
    return false;
  }
}

function getMigrationFiles() {
  try {
    const files = readdirSync(MIGRATIONS_DIR)
      .filter(file => extname(file) === '.js')
      .sort();

    logInfo(`Available migration files: ${files.length}`);
    files.forEach(file => {
      log(`  - ${file}`, 'blue');
    });

    return files;
  } catch (error) {
    logError(`Failed to read migration files: ${error.message}`);
    return [];
  }
}

async function createMigration(name) {
  if (!name) {
    logError('Migration name is required');
    return;
  }

  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
  const fileName = `${timestamp}-${name}.js`;
  const filePath = join(MIGRATIONS_DIR, fileName);

  const template = `import { QueryInterface, DataTypes } from 'sequelize';

export async function up(queryInterface) {
  // Add your migration logic here
  // Example:
  // await queryInterface.createTable('example', {
  //   id: {
  //     type: DataTypes.INTEGER,
  //     primaryKey: true,
  //     autoIncrement: true
  //   },
  //   name: {
  //     type: DataTypes.STRING,
  //     allowNull: false
  //   },
  //   createdAt: {
  //     type: DataTypes.DATE,
  //     allowNull: false
  //   },
  //   updatedAt: {
  //     type: DataTypes.DATE,
  //     allowNull: false
  //   }
  // });
}

export async function down(queryInterface) {
  // Add your rollback logic here
  // Example:
  // await queryInterface.dropTable('example');
}
`;

  try {
    // Write the migration file
    const fs = await import('fs');
    fs.writeFileSync(filePath, template);

    logSuccess(`Migration created: ${fileName}`);
    logInfo(`Location: ${filePath}`);
    logStep('Next steps:');
    log('  1. Edit the migration file with your changes', 'blue');
    log('  2. Run: npm run db:migrate', 'blue');
    log('  3. Verify changes: npm run db:migrate:status', 'blue');
  } catch (error) {
    logError(`Failed to create migration: ${error.message}`);
  }
}

async function showMigrationHelp() {
  logStep('Available Migration Commands:');
  console.log('');
  log('Migration Management:', 'bold');
  log('  npm run db:migrate                 Run pending migrations', 'green');
  log('  npm run db:migrate:undo            Undo last migration', 'yellow');
  log('  npm run db:migrate:undo:all        Undo all migrations', 'red');
  log('  npm run db:migrate:status          Show migration status', 'blue');
  log('  npm run db:migrate:create <name>   Create new migration', 'green');
  log('  npm run db:migrate:up <name>       Migrate to specific migration', 'blue');
  log('  npm run db:migrate:down <name>     Rollback to specific migration', 'yellow');
  console.log('');
  log('Database Management:', 'bold');
  log('  npm run db:create                  Create database', 'green');
  log('  npm run db:drop                    Drop database', 'red');
  log('  npm run db:reset                   Reset database (drop + create + migrate + seed)', 'yellow');
  log('  npm run db:schema:drop             Drop and recreate database schema', 'red');
  console.log('');
  log('Seeding:', 'bold');
  log('  npm run db:seed                    Run all seeders', 'green');
  log('  npm run db:seed:undo               Undo all seeders', 'yellow');
  log('  npm run db:seed:create <name>      Create new seeder', 'blue');
  console.log('');
  logStep('Migration Workflow:');
  log('1. Make changes to your models', 'blue');
  log('2. Create migration: npm run db:migrate:create <name>', 'blue');
  log('3. Edit the migration file with your changes', 'blue');
  log('4. Run migration: npm run db:migrate', 'blue');
  log('5. Verify: npm run db:migrate:status', 'blue');
  console.log('');
  logStep('Best Practices:');
  log('• Always create migrations for schema changes', 'yellow');
  log('• Test migrations in development first', 'yellow');
  log('• Always write both up() and down() methods', 'yellow');
  log('• Use descriptive migration names', 'yellow');
  log('• Keep migrations atomic and reversible', 'yellow');
  log('• Never edit existing migrations after deployment', 'red');
}

async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  logStep('🗄️  Sequelize Migration Helper');
  console.log('');

  switch (command) {
    case 'status':
      await checkDatabaseConnection();
      await getMigrationStatus();
      getMigrationFiles();
      break;

    case 'create':
      await createMigration(args[1]);
      break;

    case 'check':
      await checkDatabaseConnection();
      await getMigrationStatus();
      getMigrationFiles();
      break;

    case 'help':
    case '--help':
    case '-h':
      await showMigrationHelp();
      break;

    default:
      logError('Unknown command');
      await showMigrationHelp();
      process.exit(1);
  }

  await sequelize.close();
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  logInfo('Shutting down...');
  await sequelize.close();
  process.exit(0);
});

// Run the script
main().catch(error => {
  logError(`Script failed: ${error.message}`);
  process.exit(1);
});