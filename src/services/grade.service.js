import {
  Grade,
  Submission,
  User,
  Project,
  LtiContext,
  R<PERSON><PERSON>,
  Checkpoint,
  CheckpointProgress,
  LtiContextEnrollment
} from '../models/associations.js';
import { Op } from 'sequelize';
import logger from '../config/logger.config.js';
import ApiError from '../utils/ApiError.utils.js';
import httpStatus from 'http-status';

class GradeService {
  /**
   * Get all grades with pagination and filtering
   */
  async getGrades(req) {
    try {
      const {
        page = 1,
        limit = 10,
        projectId,
        courseId,
        studentId,
        evaluatorId,
        sortBy = 'graded_at',
        sortOrder = 'desc'
      } = req.query;

      const offset = (parseInt(page) - 1) * parseInt(limit);
      const whereClause = {};

      const includeClause = [
        {
          model: Submission,
          as: 'submission',
          include: [
            {
              model: User,
              as: 'user',
              attributes: ['id', 'name', 'email', 'profile_picture']
            },
            {
              model: Project,
              as: 'project',
              attributes: ['id', 'title', 'due_date'],
              include: [
                {
                  model: LtiContext,
                  as: 'course',
                  attributes: [
                    'id',
                    ['context_title', 'name'],
                    ['context_label', 'code']
                  ],
                  raw: true
                }
              ]
            }
          ]
        },
        {
          model: User,
          as: 'evaluator',
          attributes: ['id', 'name', 'email']
        }
      ];

      // Apply filters
      if (projectId) includeClause[0].where = { project_id: projectId };
      if (courseId) includeClause[0].include[1].where = { course_id: courseId };
      if (studentId) includeClause[0].include[0].where = { id: studentId };
      if (evaluatorId) whereClause.evaluator_id = evaluatorId;
      if (req.user.role === 'student')
        includeClause[0].include[0].where = { id: req.user.id };

      const { count, rows: grades } = await Grade.findAndCountAll({
        where: whereClause,
        include: includeClause,
        limit: parseInt(limit),
        offset,
        order: [[sortBy, sortOrder.toUpperCase()]]
      });

      const transformedGrades = grades.map(grade => ({
        id: grade.id,
        totalScore: grade.total_score,
        maxScore: grade.max_score,
        percentage: grade.percentage,
        letterGrade: grade.letter_grade,
        feedback: grade.feedback,
        rubricScores: grade.rubric_scores,
        detailedFeedback: grade.detailed_feedback,
        gradingMethod:
          grade.auto_graded_components?.grading_method || 'manual_grading',
        lastUpdatedMethod: grade.auto_graded_components?.last_updated_method,
        gradingHistory: grade.auto_graded_components?.grading_history || [],
        gradedAt: grade.graded_at,
        submission: {
          id: grade.submission.id,
          submittedAt: grade.submission.submitted_at,
          status: grade.submission.status,
          user: grade.submission.user,
          project: {
            id: grade.submission.project.id,
            title: grade.submission.project.title,
            dueDate: grade.submission.project.due_date,
            course: grade.submission.project.course
          }
        },
        evaluator: grade.evaluator,
        createdAt: grade.created_at,
        updatedAt: grade.updated_at
      }));

      return {
        grades: transformedGrades,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / parseInt(limit)),
          totalItems: count,
          itemsPerPage: parseInt(limit)
        }
      };
    } catch (error) {
      logger.error('Error getting grades:', error);
      throw error;
    }
  }

  /**
   * Get grade by ID
   */
  async getGradeById(gradeId, user) {
    try {
      const grade = await Grade.findByPk(gradeId, {
        include: [
          {
            model: Submission,
            as: 'submission',
            include: [
              {
                model: User,
                as: 'user',
                attributes: ['id', 'name', 'email', 'profile_picture']
              },
              {
                model: Project,
                as: 'project',
                include: [
                  {
                    model: LtiContext,
                    as: 'course',
                    attributes: [
                      'id',
                      ['context_title', 'name'],
                      ['context_label', 'code']
                    ],
                    raw: true
                  },
                  {
                    model: Rubric,
                    as: 'rubrics'
                  }
                ]
              }
            ]
          },
          {
            model: User,
            as: 'evaluator',
            attributes: ['id', 'name', 'email']
          }
        ]
      });

      if (!grade) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Grade not found');
      }

      // Check access permissions
      const hasAccess =
        user.role === 'admin' ||
        grade.submission.user_id === user.id ||
        grade.evaluator_id === user.id;

      if (!hasAccess) {
        throw new ApiError(httpStatus.FORBIDDEN, 'Access denied');
      }

      return {
        id: grade.id,
        totalScore: grade.total_score,
        maxScore: grade.max_score,
        percentage: grade.percentage,
        letterGrade: grade.letter_grade,
        feedback: grade.feedback,
        rubricScores: grade.rubric_scores,
        detailedFeedback: grade.detailed_feedback,
        gradingMethod:
          grade.auto_graded_components?.grading_method || 'manual_grading',
        lastUpdatedMethod: grade.auto_graded_components?.last_updated_method,
        gradingHistory: grade.auto_graded_components?.grading_history || [],
        gradedAt: grade.graded_at,
        submission: {
          id: grade.submission.id,
          submittedAt: grade.submission.submitted_at,
          status: grade.submission.status,
          notebookS3Url: grade.submission.notebook_s3_url,
          user: grade.submission.user,
          project: {
            id: grade.submission.project.id,
            title: grade.submission.project.title,
            description: grade.submission.project.description,
            dueDate: grade.submission.project.due_date,
            instructions: grade.submission.project.instructions,
            course: {
              id: grade.submission.project.course.id,
              name: grade.submission.project.course.name,
              code: grade.submission.project.course.code
            },
            rubrics:
              grade.submission.project.rubrics?.map(rubric => ({
                id: rubric.id,
                name: rubric.name,
                description: rubric.description,
                criteria: rubric.criteria,
                maxScore: rubric.max_score,
                weight: rubric.weight
              })) || []
          }
        },
        evaluator: grade.evaluator,
        createdAt: grade.created_at,
        updatedAt: grade.updated_at
      };
    } catch (error) {
      logger.error('Error getting grade by ID:', error);
      throw error;
    }
  }

  /**
   * Create or update grade
   */
  async createOrUpdateGrade(data, user) {
    try {
      const {
        submissionId,
        totalScore,
        maxScore,
        percentage,
        letterGrade,
        feedback,
        rubricScores = {}
      } = data;

      if (!submissionId) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Submission ID is required');
      }

      const submission = await Submission.findByPk(submissionId, {
        include: [
          {
            model: Project,
            as: 'project',
            include: [
              {
                model: LtiContext,
                as: 'course',
                attributes: [
                  'id',
                  ['context_title', 'name'],
                  ['context_label', 'code']
                ],
                raw: true
              }
            ]
          }
        ]
      });

      if (!submission) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Submission not found');
      }

      // Check permissions
      const hasPermission = user.role === 'admin';

      if (!hasPermission) {
        throw new ApiError(httpStatus.FORBIDDEN, 'Access denied');
      }

      if (
        submission.status !== 'submitted' &&
        submission.status !== 'grading'
      ) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Can only grade submitted submissions'
        );
      }

      if (totalScore < 0 || (maxScore && totalScore > maxScore)) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Total score must be between 0 and maximum score'
        );
      }

      // Calculate values
      let calculatedPercentage = percentage;
      if (!calculatedPercentage && maxScore && maxScore > 0) {
        calculatedPercentage = (totalScore / maxScore) * 100;
      }

      let calculatedLetterGrade = letterGrade;
      if (!calculatedLetterGrade && calculatedPercentage !== undefined) {
        calculatedLetterGrade = this.calculateLetterGrade(calculatedPercentage);
      }

      // Find or create grade
      let grade = await Grade.findOne({
        where: { submission_id: submissionId }
      });

      const gradeData = {
        total_score: totalScore,
        max_score: maxScore,
        percentage: calculatedPercentage,
        letter_grade: calculatedLetterGrade,
        feedback,
        rubric_scores: rubricScores,
        evaluator_id: user.id,
        graded_at: new Date(),
        auto_graded_components: { grading_method: 'manual_grading' }
      };

      if (grade) {
        await grade.update(gradeData);
      } else {
        grade = await Grade.create({
          submission_id: submissionId,
          ...gradeData
        });
      }

      await submission.update({ status: 'graded' });

      return {
        id: grade.id,
        totalScore: grade.total_score,
        maxScore: grade.max_score,
        percentage: grade.percentage,
        letterGrade: grade.letter_grade,
        feedback: grade.feedback,
        gradedAt: grade.graded_at
      };
    } catch (error) {
      logger.error('Error creating/updating grade:', error);
      throw error;
    }
  }

  /**
   * Update grade
   */
  async updateGrade(gradeId, data, user) {
    try {
      const {
        totalScore,
        maxScore,
        percentage,
        letterGrade,
        feedback,
        rubricScores
      } = data;

      const grade = await Grade.findByPk(gradeId, {
        include: [
          {
            model: Submission,
            as: 'submission',
            include: [
              {
                model: Project,
                as: 'project',
                include: [
                  {
                    model: LtiContext,
                    as: 'course',
                    attributes: [
                      'id',
                      ['context_title', 'name'],
                      ['context_label', 'code']
                    ],
                    raw: true
                  }
                ]
              }
            ]
          }
        ]
      });

      if (!grade) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Grade not found');
      }

      // Check permissions
      const hasPermission =
        user.role === 'admin' ||
        grade.evaluator_id === user.id;

      if (!hasPermission) {
        throw new ApiError(httpStatus.FORBIDDEN, 'Access denied');
      }

      if (
        totalScore !== undefined &&
        (totalScore < 0 || (maxScore && totalScore > maxScore))
      ) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Total score must be between 0 and maximum score'
        );
      }

      // Build update data
      const updateData = {};
      if (totalScore !== undefined) updateData.total_score = totalScore;
      if (maxScore !== undefined) updateData.max_score = maxScore;
      if (percentage !== undefined) updateData.percentage = percentage;
      if (letterGrade !== undefined) updateData.letter_grade = letterGrade;
      if (feedback !== undefined) updateData.feedback = feedback;
      if (rubricScores !== undefined) updateData.rubric_scores = rubricScores;

      // Recalculate percentage if needed
      if (totalScore !== undefined || maxScore !== undefined) {
        const newTotal =
          totalScore !== undefined ? totalScore : grade.total_score;
        const newMax = maxScore !== undefined ? maxScore : grade.max_score;
        if (newMax && newMax > 0) {
          updateData.percentage = (newTotal / newMax) * 100;
        }
      }

      // Recalculate letter grade if needed
      if (updateData.percentage !== undefined) {
        updateData.letter_grade = this.calculateLetterGrade(
          updateData.percentage
        );
      }

      updateData.evaluator_id = user.id;
      updateData.graded_at = new Date();

      await grade.update(updateData);

      return {
        id: grade.id,
        totalScore: grade.total_score,
        maxScore: grade.max_score,
        percentage: grade.percentage,
        letterGrade: grade.letter_grade,
        feedback: grade.feedback,
        gradedAt: grade.graded_at
      };
    } catch (error) {
      logger.error('Error updating grade:', error);
      throw error;
    }
  }

  /**
   * Delete grade
   */
  async deleteGrade(gradeId, user) {
    try {
      const grade = await Grade.findByPk(gradeId, {
        include: [
          {
            model: Submission,
            as: 'submission',
            include: [
              {
                model: Project,
                as: 'project',
                include: [
                  {
                    model: LtiContext,
                    as: 'course',
                    attributes: [
                      'id',
                      ['context_title', 'name'],
                      ['context_label', 'code']
                    ],
                    raw: true
                  }
                ]
              }
            ]
          }
        ]
      });

      if (!grade) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Grade not found');
      }

      // Check permissions
      const hasPermission =
        user.role === 'admin' ||
        grade.evaluator_id === user.id;

      if (!hasPermission) {
        throw new ApiError(httpStatus.FORBIDDEN, 'Access denied');
      }

      // Update submission status back to submitted
      await grade.submission.update({ status: 'submitted' });

      // Delete grade
      await grade.destroy();

      return { deletedGradeId: gradeId };
    } catch (error) {
      logger.error('Error deleting grade:', error);
      throw error;
    }
  }

  /**
   * Get grading queue
   */
  async getGradingQueue(req) {
    try {
      const {
        page = 1,
        limit = 10,
        courseId,
        projectId,
        sortBy = 'submitted_at',
        sortOrder = 'asc'
      } = req.query;

      const offset = (parseInt(page) - 1) * parseInt(limit);
      const whereClause = { status: 'submitted' };

      const includeClause = [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'name', 'email', 'profile_picture']
        },
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'title', 'due_date', 'difficulty_level'],
          include: [
            {
              model: LtiContext,
              as: 'course',
              attributes: [
                'id',
                ['context_title', 'name'],
                ['context_label', 'code']
              ],
              raw: true
            }
          ]
        },
        {
          model: Grade,
          as: 'grade',
          required: false
        }
      ];

      // Apply filters
      if (projectId) whereClause.project_id = projectId;
      if (courseId) includeClause[1].where = { course_id: courseId };
      // Remove instructor filter since LtiContext doesn't have instructor_id

      const { count, rows: submissions } = await Submission.findAndCountAll({
        where: whereClause,
        include: includeClause,
        limit: parseInt(limit),
        offset,
        order: [[sortBy, sortOrder.toUpperCase()]]
      });

      const transformedSubmissions = submissions.map(submission => ({
        id: submission.id,
        submittedAt: submission.submitted_at,
        isGraded: !!submission.grade,
        user: submission.user,
        project: {
          id: submission.project.id,
          title: submission.project.title,
          dueDate: submission.project.due_date,
          difficultyLevel: submission.project.difficulty_level,
          course: submission.project.course
        },
        grade: submission.grade
          ? {
              id: submission.grade.id,
              totalScore: submission.grade.total_score,
              maxScore: submission.grade.max_score,
              percentage: submission.grade.percentage,
              letterGrade: submission.grade.letter_grade
            }
          : null
      }));

      return {
        submissions: transformedSubmissions,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / parseInt(limit)),
          totalItems: count,
          itemsPerPage: parseInt(limit)
        }
      };
    } catch (error) {
      logger.error('Error getting grading queue:', error);
      throw error;
    }
  }

  /**
   * Get grade statistics
   */
  async getGradeStatistics(projectId, user) {
    try {
      const project = await Project.findByPk(projectId, {
        include: [
          {
            model: LtiContext,
            as: 'course',
            attributes: [
              'id',
              ['context_title', 'name'],
              ['context_label', 'code']
            ],
            raw: true
          }
        ]
      });

      if (!project) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Project not found');
      }

      // Check permissions
      const hasPermission = user.role === 'admin';

      if (!hasPermission) {
        throw new ApiError(httpStatus.FORBIDDEN, 'Access denied');
      }

      // Get grade statistics
      const gradeStats = await Grade.findAll({
        include: [
          {
            model: Submission,
            as: 'submission',
            where: { project_id: projectId },
            attributes: []
          }
        ],
        attributes: [
          [
            Grade.sequelize.fn('COUNT', Grade.sequelize.col('Grade.id')),
            'totalGrades'
          ],
          [
            Grade.sequelize.fn('AVG', Grade.sequelize.col('percentage')),
            'averagePercentage'
          ],
          [
            Grade.sequelize.fn('MIN', Grade.sequelize.col('percentage')),
            'minPercentage'
          ],
          [
            Grade.sequelize.fn('MAX', Grade.sequelize.col('percentage')),
            'maxPercentage'
          ],
          [
            Grade.sequelize.fn('STDDEV', Grade.sequelize.col('percentage')),
            'standardDeviation'
          ]
        ]
      });

      // Get grade distribution
      const gradeDistribution = await Grade.findAll({
        include: [
          {
            model: Submission,
            as: 'submission',
            where: { project_id: projectId },
            attributes: []
          }
        ],
        attributes: [
          'letter_grade',
          [
            Grade.sequelize.fn('COUNT', Grade.sequelize.col('Grade.id')),
            'count'
          ]
        ],
        group: ['letter_grade']
      });

      const stats = gradeStats[0];

      return {
        projectId,
        projectTitle: project.title,
        overview: {
          totalGrades: parseInt(stats.getDataValue('totalGrades')) || 0,
          averagePercentage:
            parseFloat(stats.getDataValue('averagePercentage')) || 0,
          minPercentage: parseFloat(stats.getDataValue('minPercentage')) || 0,
          maxPercentage: parseFloat(stats.getDataValue('maxPercentage')) || 0,
          standardDeviation:
            parseFloat(stats.getDataValue('standardDeviation')) || 0
        },
        distribution: gradeDistribution.map(item => ({
          grade: item.letter_grade,
          count: parseInt(item.getDataValue('count'))
        }))
      };
    } catch (error) {
      logger.error('Error getting grade statistics:', error);
      throw error;
    }
  }

  /**
   * Bulk grade submissions
   */
  async bulkGradeSubmissions(data, user) {
    try {
      const { submissions } = data;

      if (!Array.isArray(submissions) || submissions.length === 0) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Submissions array is required and must not be empty'
        );
      }

      const results = {
        processedCount: 0,
        successCount: 0,
        failureCount: 0,
        results: []
      };

      for (const submissionGrade of submissions) {
        try {
          results.processedCount++;
          const result = await this.createOrUpdateGrade(submissionGrade, user);
          results.successCount++;
          results.results.push({
            submissionId: submissionGrade.submissionId,
            gradeId: result.id,
            status: 'success'
          });
        } catch (error) {
          results.failureCount++;
          results.results.push({
            submissionId: submissionGrade.submissionId,
            status: 'error',
            error: error.message
          });
        }
      }

      return results;
    } catch (error) {
      logger.error('Error bulk grading submissions:', error);
      throw error;
    }
  }

  /**
   * Create rubric-based grade with checkpoint validation
   */
  async createRubricBasedGrade(data, user, options = {}) {
    try {
      const {
        submissionId,
        checkpointRubrics, // Array of {checkpointId, rubrics: [{rubricId, criteriaScores}]}
        overallFeedback,
        totalPoints,
        maxPoints,
        strictValidation = true // Allow flexible criterion matching
      } = data;

      const { allowPartialMatch = false } = options;

      // Find submission with project and checkpoints
      const submission = await Submission.findByPk(submissionId, {
        include: [
          {
            model: Project,
            as: 'project',
            include: [
              {
                model: LtiContext,
                as: 'course',
                attributes: [
                  'id',
                  ['context_title', 'name'],
                  ['context_label', 'code']
                ],
                raw: true
              },
              {
                model: Checkpoint,
                as: 'checkpoints',
                include: [
                  {
                    model: Rubric,
                    as: 'rubrics'
                  }
                ]
              }
            ]
          }
        ]
      });

      if (!submission) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Submission not found');
      }

      // Check permissions
      const isInstructorAssigned =
        submission.project.instructor_id &&
        submission.project.instructor_id.includes(user.id);
      const isTAAssigned =
        submission.project.teaching_ass_id &&
        submission.project.teaching_ass_id.includes(user.id);

      if (!isInstructorAssigned && !isTAAssigned) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Access denied. Only instructors and TAs assigned to this project can access this information.'
        );
      }

      let calculatedTotalScore = 0;
      let calculatedMaxScore = 0;
      const detailedRubricScores = {};
      const validationErrors = [];

      // Validate and calculate scores for each checkpoint with multiple rubrics
      for (const checkpointRubric of checkpointRubrics) {
        const {
          checkpointId,
          rubrics,
          totalPoints: checkpointTotalPoints,
          maxPoints: checkpointMaxPoints
        } = checkpointRubric;

        // Find the checkpoint
        const checkpoint = submission.project.checkpoints.find(
          cp => cp.id === checkpointId
        );
        if (!checkpoint) {
          validationErrors.push(`Checkpoint ${checkpointId} not found`);
          continue;
        }

        const checkpointRubrics = [];
        let checkpointTotalScore = 0;
        let checkpointMaxScore = 0;

        // Process each rubric for this checkpoint
        for (const rubricData of rubrics) {
          const {
            rubricId,
            criteriaScores,
            totalScore: rubricTotalScore,
            maxPoints: rubricMaxPoints
          } = rubricData;

          const rubric = checkpoint.rubrics.find(r => r.id === rubricId);
          if (!rubric) {
            validationErrors.push(
              `Rubric ${rubricId} not found for checkpoint ${checkpoint.title}`
            );
            continue;
          }

          // Calculate scores for this rubric first
          const { totalScore, maxScore } = this.calculateRubricTotalScore(
            rubric,
            criteriaScores
          );

          // Validate criteria scores for this rubric
          const rubricValidation = this.validateCheckpointRubricScores(
            rubric,
            criteriaScores,
            { allowPartialMatch, strictValidation }
          );
          if (!rubricValidation.isValid) {
            if (strictValidation) {
              validationErrors.push(
                ...rubricValidation.errors.map(
                  err =>
                    `Checkpoint ${checkpoint.title} - Rubric ${rubric.title}: ${err}`
                )
              );
            } else {
              // Log warnings but continue processing
              logger.warn(
                `Rubric validation warnings for ${rubric.title}:`,
                rubricValidation.errors
              );
            }
          }

          // Validate rubric total score if provided
          if (
            rubricTotalScore !== undefined &&
            Math.abs(rubricTotalScore - totalScore) > 0.01
          ) {
            validationErrors.push(
              `Rubric total score ${rubricTotalScore} does not match calculated total ${totalScore} for rubric '${rubric.title}'`
            );
          }

          // Validate rubric max points if provided
          if (
            rubricMaxPoints !== undefined &&
            Math.abs(rubricMaxPoints - maxScore) > 0.01
          ) {
            validationErrors.push(
              `Rubric max points ${rubricMaxPoints} does not match calculated max ${maxScore} for rubric '${rubric.title}'`
            );
          }

          // Validate total score is less than or equal to max points
          if (totalScore > maxScore) {
            validationErrors.push(
              `Rubric total score ${totalScore} exceeds max points ${maxScore} for rubric '${rubric.title}'`
            );
          }

          checkpointTotalScore += totalScore;
          checkpointMaxScore += maxScore;

          // Store rubric details
          checkpointRubrics.push({
            rubricId: rubric.id,
            rubricTitle: rubric.title,
            criteriaScores,
            totalScore,
            maxScore
          });
        }

        // Validate checkpoint total points if provided
        if (
          checkpointTotalPoints !== undefined &&
          Math.abs(checkpointTotalPoints - checkpointTotalScore) > 0.01
        ) {
          validationErrors.push(
            `Checkpoint total points ${checkpointTotalPoints} does not match calculated total ${checkpointTotalScore} for checkpoint '${checkpoint.title}'`
          );
        }

        // Validate checkpoint max points if provided
        if (
          checkpointMaxPoints !== undefined &&
          Math.abs(checkpointMaxPoints - checkpointMaxScore) > 0.01
        ) {
          validationErrors.push(
            `Checkpoint max points ${checkpointMaxPoints} does not match calculated max ${checkpointMaxScore} for checkpoint '${checkpoint.title}'`
          );
        }

        // Validate checkpoint total score is less than or equal to max points
        if (checkpointTotalScore > checkpointMaxScore) {
          validationErrors.push(
            `Checkpoint total score ${checkpointTotalScore} exceeds max points ${checkpointMaxScore} for checkpoint '${checkpoint.title}'`
          );
        }

        // Add checkpoint totals to overall totals
        calculatedTotalScore += checkpointTotalScore;
        calculatedMaxScore += checkpointMaxScore;

        // Store detailed scores by checkpoint
        detailedRubricScores[checkpointId] = {
          checkpointTitle: checkpoint.title,
          rubrics: checkpointRubrics,
          checkpointTotalScore,
          checkpointMaxScore,
          checkpointFeedback: checkpointRubric.checkpointFeedback || checkpointRubric.feedback || ''
        };
      }

      // Check for validation errors
      if (validationErrors.length > 0) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          `${validationErrors.join(', ')}`
        );
      }

      // Validate total points if provided
      if (
        totalPoints !== undefined &&
        Math.abs(totalPoints - calculatedTotalScore) > 0.01
      ) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          `Total points mismatch. Calculated: ${calculatedTotalScore}, Provided: ${totalPoints}`
        );
      }

      // Validate max points if provided
      if (
        maxPoints !== undefined &&
        Math.abs(maxPoints - calculatedMaxScore) > 0.01
      ) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          `Max points mismatch. Calculated: ${calculatedMaxScore}, Provided: ${maxPoints}`
        );
      }

      // Validate total score is less than or equal to max points
      if (calculatedTotalScore > calculatedMaxScore) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          `Total score ${calculatedTotalScore} exceeds max points ${calculatedMaxScore}`
        );
      }

      const percentage =
        calculatedMaxScore > 0
          ? (calculatedTotalScore / calculatedMaxScore) * 100
          : 0;
      const letterGrade = this.calculateLetterGrade(percentage);

      // Create or update grade
      let grade = await Grade.findOne({
        where: { submission_id: submissionId }
      });

      const gradeData = {
        total_score: calculatedTotalScore,
        max_score: calculatedMaxScore,
        percentage,
        letter_grade: letterGrade,
        feedback: overallFeedback,
        rubric_scores: detailedRubricScores,
        evaluator_id: user.id,
        graded_at: new Date(),
        auto_graded_components: {
          grading_method: 'checkpoint_rubric_based',
          last_updated_method: 'checkpoint_rubric_based'
        }
      };

      let message = 'Grade created successfully using rubric validation';
      if (grade) {
        message = 'Grade updated successfully using rubric validation';
        await grade.update(gradeData);
      } else {
        grade = await Grade.create({
          submission_id: submissionId,
          ...gradeData
        });
      }

      await submission.update({ status: 'graded' });

      // Update checkpoint progress for graded checkpoints
      const studentId = submission.user_id;

      for (const checkpointId of Object.keys(detailedRubricScores)) {
        await CheckpointProgress.upsert({
          checkpoint_id: checkpointId,
          user_id: studentId,
          project_id: submission.project_id,
          status: 'completed',
          completed_at: new Date(),
          last_activity: new Date()
        });
      }

      return {
        id: grade.id,
        totalScore: grade.total_score,
        maxScore: grade.max_score,
        percentage: grade.percentage,
        letterGrade: grade.letter_grade,
        feedback: grade.feedback,
        checkpointBreakdown: detailedRubricScores,
        gradedAt: grade.graded_at,
        message
      };
    } catch (error) {
      logger.error('Error creating checkpoint rubric-based grade:', error);
      throw error;
    }
  }

  /**
   * Validate checkpoint rubric scores against rubric criteria
   */
  validateCheckpointRubricScores(rubric, criteriaScores, options = {}) {
    const { allowPartialMatch = false, strictValidation = true } = options;
    const errors = [];
    const warnings = [];
    const matchedCriteria = [];

    if (!Array.isArray(criteriaScores)) {
      errors.push('Criteria scores must be an array');
      return { isValid: false, errors, warnings };
    }

    // Validate each criterion score
    for (const scoreData of criteriaScores) {
      const { criterionName, score, maxPoints } = scoreData;

      if (!criterionName) {
        errors.push('Criterion name is required');
        continue;
      }

      // Find exact match first
      let criterion = rubric.criteria.find(c => c.name === criterionName);
      let matchType = 'exact';

      // If no exact match and partial matching is allowed, try fuzzy matching
      if (!criterion && allowPartialMatch) {
        criterion = rubric.criteria.find(
          c =>
            c.name.toLowerCase().includes(criterionName.toLowerCase()) ||
            criterionName.toLowerCase().includes(c.name.toLowerCase())
        );
        matchType = 'partial';
      }

      if (!criterion) {
        const availableCriteria = rubric.criteria.map(c => c.name);
        const similarCriterion = availableCriteria.find(
          name =>
            name.toLowerCase().includes(criterionName.toLowerCase()) ||
            criterionName.toLowerCase().includes(name.toLowerCase())
        );

        const suggestion = similarCriterion
          ? ` Did you mean '${similarCriterion}'?`
          : ` Available criteria: ${availableCriteria.join(', ')}`;

        errors.push(
          `Criterion '${criterionName}' not found in rubric.${suggestion}`
        );
        continue;
      }

      // Log partial matches as warnings
      if (matchType === 'partial') {
        warnings.push(
          `Criterion '${criterionName}' matched to '${criterion.name}' using partial matching`
        );
      }

      matchedCriteria.push(criterion.name);

      // Validate score
      if (typeof score !== 'number' || score < 0) {
        errors.push(
          `Invalid score for criterion '${criterionName}': must be a non-negative number`
        );
        continue;
      }

      // Validate maxPoints against rubric criteria if provided
      if (
        maxPoints !== undefined &&
        Math.abs(maxPoints - criterion.points) > 0.01
      ) {
        errors.push(
          `Max points ${maxPoints} for criterion '${criterionName}' does not match rubric definition ${criterion.points}`
        );
        continue;
      }

      // Validate against criterion max points
      const criterionMaxPoints = criterion.points;
      if (score > criterionMaxPoints) {
        errors.push(
          `Score ${score} for criterion '${criterionName}' exceeds maximum points ${criterionMaxPoints}`
        );
      }
    }

    // Check if all required criteria are provided (only in strict mode)
    if (strictValidation) {
      const requiredCriteria = rubric.criteria.map(c => c.name);
      const missingCriteria = requiredCriteria.filter(
        rc => !matchedCriteria.includes(rc)
      );

      if (missingCriteria.length > 0) {
        errors.push(
          `Missing scores for criteria: ${missingCriteria.join(', ')}`
        );
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      matchedCriteria
    };
  }

  /**
   * Calculate total score for a rubric based on criteria scores
   */
  calculateRubricTotalScore(rubric, criteriaScores) {
    let totalScore = 0;
    let maxScore = 0;

    for (const scoreData of criteriaScores) {
      const { criterionName, score, maxPoints } = scoreData;
      const criterion = rubric.criteria.find(c => c.name === criterionName);

      if (criterion) {
        totalScore += score || 0;
        maxScore += maxPoints || criterion.points || 0;
      }
    }

    return { totalScore, maxScore };
  }

  /**
   * Validate checkpoint total points against rubric totals
   */
  validateCheckpointTotalPoints(checkpoint, rubrics, providedTotal) {
    const calculatedTotal = rubrics.reduce((sum, rubricData) => {
      const rubric = checkpoint.rubrics.find(r => r.id === rubricData.rubricId);
      if (rubric) {
        const { maxScore } = this.calculateRubricTotalScore(
          rubric,
          rubricData.criteriaScores
        );
        return sum + maxScore;
      }
      return sum;
    }, 0);

    if (
      providedTotal !== undefined &&
      Math.abs(providedTotal - calculatedTotal) > 0.01
    ) {
      return {
        isValid: false,
        error: `Checkpoint total points ${providedTotal} does not match calculated total ${calculatedTotal}`
      };
    }

    return { isValid: true, calculatedTotal };
  }
  validateCheckpointRubricScores(rubric, criteriaScores) {
    const errors = [];
    const criteriaMap = {};

    // Create map of rubric criteria
    rubric.criteria.forEach(criterion => {
      criteriaMap[criterion.name] = criterion;
    });

    // Handle array format: [{criterionName: 'name', score: 23}]
    if (Array.isArray(criteriaScores)) {
      for (const criteriaScore of criteriaScores) {
        const { criterionName, score } = criteriaScore;
        const criterion = criteriaMap[criterionName];

        if (!criterion) {
          errors.push(`Criterion '${criterionName}' not found in rubric`);
          continue;
        }

        if (
          typeof score !== 'number' ||
          score < 0 ||
          score > criterion.points
        ) {
          errors.push(
            `Score for '${criterionName}' must be between 0 and ${criterion.points}`
          );
        }
      }
    } else {
      // Handle object format: {criterionName: score}
      for (const [criterionName, score] of Object.entries(criteriaScores)) {
        const criterion = criteriaMap[criterionName];

        if (!criterion) {
          errors.push(`Criterion '${criterionName}' not found in rubric`);
          continue;
        }

        if (
          typeof score !== 'number' ||
          score < 0 ||
          score > criterion.points
        ) {
          errors.push(
            `Score for '${criterionName}' must be between 0 and ${criterion.points}`
          );
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate rubric scores against rubric criteria
   */
  validateRubricScores(rubric, criteriaScores) {
    const errors = [];
    const criteriaMap = {};

    // Create map of rubric criteria
    rubric.criteria.forEach(criterion => {
      criteriaMap[criterion.name] = criterion;
    });

    // Validate each provided score
    for (const [criterionName, score] of Object.entries(criteriaScores)) {
      const criterion = criteriaMap[criterionName];

      if (!criterion) {
        errors.push(`Criterion '${criterionName}' not found in rubric`);
        continue;
      }

      if (typeof score !== 'number' || score < 0 || score > criterion.points) {
        errors.push(
          `Score for '${criterionName}' must be between 0 and ${criterion.points}`
        );
      }
    }

    // Check for missing required criteria
    const providedCriteria = Object.keys(criteriaScores);
    const requiredCriteria = rubric.criteria.map(c => c.name);
    const missingCriteria = requiredCriteria.filter(
      name => !providedCriteria.includes(name)
    );

    if (missingCriteria.length > 0) {
      errors.push(`Missing scores for criteria: ${missingCriteria.join(', ')}`);
    }

    return {
      isValid: errors.length === 0,
      message:
        errors.length > 0
          ? 'Rubric validation failed'
          : 'Rubric validation passed',
      errors
    };
  }

  /**
   * Calculate total score from rubric criteria
   */
  calculateRubricTotalScore(rubric, criteriaScores) {
    let totalScore = 0;
    let maxScore = 0;

    if (Array.isArray(criteriaScores)) {
      // Handle array format: [{criterionName: 'name', score: 23}]
      const scoreMap = {};
      criteriaScores.forEach(item => {
        scoreMap[item.criterionName] = item.score;
      });

      rubric.criteria.forEach(criterion => {
        const score = scoreMap[criterion.name] || 0;
        totalScore += score;
        maxScore += criterion.points;
      });
    } else {
      // Handle object format: {criterionName: score}
      rubric.criteria.forEach(criterion => {
        const score = criteriaScores[criterion.name] || 0;
        totalScore += score;
        maxScore += criterion.points;
      });
    }

    return { totalScore, maxScore };
  }

  /**
   * Calculate letter grade using rubric's grading scale
   */
  calculateLetterGradeFromRubric(rubric, percentage) {
    if (rubric.grading_scale) {
      for (const [grade, range] of Object.entries(rubric.grading_scale)) {
        if (percentage >= range.min && percentage <= range.max) {
          return grade;
        }
      }
    }

    // Fallback to default grading scale
    return this.calculateLetterGrade(percentage);
  }

  /**
   * Get rubric data for submission grading
   */
  async getSubmissionRubricData(submissionId, userId) {
    try {
      const submission = await Submission.findByPk(submissionId, {
        include: [
          {
            model: Project,
            as: 'project',
            include: [
              {
                model: LtiContext,
                as: 'course',
                attributes: [
                  'id',
                  ['context_title', 'name'],
                  ['context_label', 'code']
                ],
                raw: true
              },
              {
                model: Rubric,
                as: 'rubrics'
              }
            ]
          },
          {
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email']
          }
        ]
      });

      if (!submission) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Submission not found');
      }

      // Check existing grade
      const existingGrade = await Grade.findOne({
        where: { submission_id: submissionId }
      });

      const rubrics = submission.project.rubrics || [];

      const rubricData = rubrics.map(rubric => ({
        id: rubric.id,
        title: rubric.title,
        description: rubric.description,
        totalPoints: rubric.total_points,
        gradingScale: rubric.grading_scale,
        criteria: rubric.criteria.map(criterion => ({
          name: criterion.name,
          description: criterion.description,
          maxScore: criterion.points,
          currentScore: existingGrade?.rubric_scores?.[criterion.name] || 0
        }))
      }));

      return {
        submission: {
          id: submission.id,
          submittedAt: submission.submitted_at,
          status: submission.status,
          user: submission.user
        },
        project: {
          id: submission.project.id,
          title: submission.project.title,
          description: submission.project.description
        },
        rubrics: rubricData,
        existingGrade: existingGrade
          ? {
              id: existingGrade.id,
              totalScore: existingGrade.total_score,
              maxScore: existingGrade.max_score,
              percentage: existingGrade.percentage,
              letterGrade: existingGrade.letter_grade,
              feedback: existingGrade.feedback,
              rubricScores: existingGrade.rubric_scores,
              detailedFeedback: existingGrade.detailed_feedback
            }
          : null
      };
    } catch (error) {
      logger.error('Error getting submission rubric data:', error);
      throw error;
    }
  }

  /**
   * Get grading history for a submission
   */
  async getSubmissionGradingHistory(submissionId, user) {
    try {
      const grade = await Grade.findOne({
        where: { submission_id: submissionId },
        include: [
          {
            model: Submission,
            as: 'submission',
            include: [
              {
                model: Project,
                as: 'project',
                include: [
                  {
                    model: LtiContext,
                    as: 'course',
                    attributes: [
                      'id',
                      ['context_title', 'name'],
                      ['context_label', 'code']
                    ],
                    raw: true
                  }
                ]
              },
              {
                model: User,
                as: 'user',
                attributes: ['id', 'name', 'email']
              }
            ]
          }
        ]
      });

      if (!grade) {
        throw new ApiError(
          httpStatus.NOT_FOUND,
          'Grade not found for this submission'
        );
      }

      // Check permissions
      const hasPermission =
        user.role === 'admin' ||
        grade.evaluator_id === user.id;

      if (!hasPermission) {
        throw new ApiError(httpStatus.FORBIDDEN, 'Access denied');
      }

      const gradingHistory =
        grade.auto_graded_components?.grading_history || [];

      // Get evaluator details for each history entry
      const historyWithEvaluators = await Promise.all(
        gradingHistory.map(async entry => {
          const evaluator = await User.findByPk(entry.evaluator_id, {
            attributes: ['id', 'name', 'email']
          });
          return {
            ...entry,
            evaluator
          };
        })
      );

      return {
        submissionId,
        currentGradingMethod:
          grade.auto_graded_components?.grading_method || 'manual_grading',
        lastUpdatedMethod: grade.auto_graded_components?.last_updated_method,
        totalHistoryEntries: gradingHistory.length,
        gradingHistory: historyWithEvaluators,
        currentGrade: {
          id: grade.id,
          totalScore: grade.total_score,
          maxScore: grade.max_score,
          percentage: grade.percentage,
          letterGrade: grade.letter_grade,
          feedback: grade.feedback,
          gradedAt: grade.graded_at
        },
        submission: {
          id: grade.submission.id,
          user: grade.submission.user,
          project: {
            id: grade.submission.project.id,
            title: grade.submission.project.title
          }
        }
      };
    } catch (error) {
      logger.error('Error getting submission grading history:', error);
      throw error;
    }
  }

  /**
   * Get submission progress details with checkpoint and rubric information
   */
  async getSubmissionProgressDetails(submissionId, user) {
    try {
      // Get submission with project and user details
      const submission = await Submission.findByPk(submissionId, {
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email']
          },
          {
            model: Project,
            as: 'project',
            include: [
              {
                model: LtiContext,
                as: 'course',
                attributes: [
                  'id',
                  ['context_title', 'name'],
                  ['context_label', 'code']
                ],
                raw: true
              },
              {
                model: Checkpoint,
                as: 'checkpoints',
                include: [
                  {
                    model: Rubric,
                    as: 'rubrics'
                  }
                ]
              }
            ]
          }
        ]
      });

      if (!submission) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Submission not found');
      }

      // Check permissions - only instructors and TAs assigned to this project
      const isInstructorAssigned =
        submission.project.instructor_id &&
        submission.project.instructor_id.includes(user.id);
      const isTAAssigned =
        submission.project.teaching_ass_id &&
        submission.project.teaching_ass_id.includes(user.id);

      if (!isInstructorAssigned && !isTAAssigned) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Access denied. Only instructors and TAs assigned to this project can access this information.'
        );
      }

      const projectId = submission.project_id;
      const studentId = submission.user_id;

      // Get existing grade if available
      const existingGrade = await Grade.findOne({
        where: { submission_id: submissionId }
      });

      // Get checkpoint progress for this student and project
      const checkpointProgress = await CheckpointProgress.findAll({
        where: {
          project_id: projectId,
          user_id: studentId
        },
        include: [
          {
            model: Checkpoint,
            as: 'checkpoint',
            include: [
              {
                model: Rubric,
                as: 'rubrics'
              }
            ]
          }
        ]
      });

      // Process checkpoints with completion status and rubric details
      const checkpointDetails = submission.project.checkpoints.map(
        checkpoint => {
          const progress = checkpointProgress.find(
            cp => cp.checkpoint_id === checkpoint.id
          );
          const isCompleted =
            progress && ['completed', 'submitted'].includes(progress.status);

          // Process rubrics for this checkpoint
          const rubricDetails = checkpoint.rubrics.map(rubric => {
            const totalPoints = rubric.criteria.reduce(
              (sum, criterion) => sum + criterion.points,
              0
            );

            const rubricData = {
              id: rubric.id,
              title: rubric.title,
              description: rubric.description,
              criteria: rubric.criteria.map(criterion => {
                const criteriaData = {
                  name: criterion.name,
                  description: criterion.description,
                  points: criterion.points
                };

                // Add scores if grade exists
                if (existingGrade?.rubric_scores) {
                  const checkpointScores =
                    existingGrade.rubric_scores[checkpoint.id];
                  if (checkpointScores?.rubrics) {
                    const rubricScore = checkpointScores.rubrics.find(
                      r => r.rubricId === rubric.id
                    );
                    if (rubricScore?.criteriaScores) {
                      const scoreData = Array.isArray(
                        rubricScore.criteriaScores
                      )
                        ? rubricScore.criteriaScores.find(
                            cs => cs.criterionName === criterion.name
                          )
                        : rubricScore.criteriaScores[criterion.name];
                      criteriaData.currentScore =
                        scoreData?.score || scoreData || 0;
                    }
                  }
                }

                return criteriaData;
              }),
              totalPoints,
              isTemplate: rubric.is_template
            };

            return rubricData;
          });

          // Get checkpoint feedback from grade if available
          let checkpointFeedback = '';
          let checkpointTotalScore = 0;
          let checkpointMaxScore = 0;
          
          if (existingGrade?.rubric_scores?.[checkpoint.id]) {
            const checkpointData = existingGrade.rubric_scores[checkpoint.id];
            checkpointFeedback = checkpointData.checkpointFeedback || '';
            checkpointTotalScore = checkpointData.checkpointTotalScore || 0;
            checkpointMaxScore = checkpointData.checkpointMaxScore || 0;
          }

          return {
            id: checkpoint.id,
            title: checkpoint.title,
            description: checkpoint.description,
            checkpointNumber: checkpoint.checkpoint_number,
            dueDate: checkpoint.due_date,
            weightPercentage: checkpoint.weight_percentage,
            isRequired: checkpoint.is_required,
            status: checkpoint.status,
            isCompleted: isCompleted || false,
            checkpointFeedback,
            checkpointTotalScore,
            checkpointMaxScore,
            progress: progress
              ? {
                  id: progress.id,
                  status: progress.status,
                  startedAt: progress.started_at,
                  submittedAt: progress.submitted_at,
                  completedAt: progress.completed_at,
                  timeSpentMinutes: progress.time_spent_minutes,
                  lastActivity: progress.last_activity
                }
              : {},
            rubrics: rubricDetails
          };
        }
      );

      // Calculate overall progress statistics
      const totalCheckpoints = checkpointDetails.length;
      const completedCheckpoints = checkpointDetails.filter(
        cp => cp.isCompleted
      ).length;
      const progressPercentage =
        totalCheckpoints > 0
          ? (completedCheckpoints / totalCheckpoints) * 100
          : 0;

      // Calculate total rubric points
      const totalRubricPoints = checkpointDetails.reduce((sum, checkpoint) => {
        return (
          sum +
          checkpoint.rubrics.reduce(
            (rubricSum, rubric) => rubricSum + rubric.totalPoints,
            0
          )
        );
      }, 0);

      const responseData = {
        submission: {
          id: submission.id,
          status: submission.status,
          submittedAt: submission.submitted_at,
          timeSpent: submission.time_spent,
          attempts: submission.attempts
        },
        student: {
          id: submission.user.id,
          name: submission.user.name,
          email: submission.user.email
        },
        project: {
          id: submission.project.id,
          title: submission.project.title,
          description: submission.project.description,
          dueDate: submission.project.due_date,
          course: submission.project.course
        },
        progressSummary: {
          totalCheckpoints,
          completedCheckpoints,
          progressPercentage: Math.round(progressPercentage * 100) / 100,
          totalRubricPoints
        },
        checkpoints: checkpointDetails
      };

      // Add grade details if available
      if (existingGrade) {
        responseData.gradeDetails = {
          id: existingGrade.id,
          totalScore: existingGrade.total_score,
          maxScore: existingGrade.max_score,
          percentage: existingGrade.percentage,
          letterGrade: existingGrade.letter_grade,
          overallFeedback: existingGrade.feedback,
          gradedAt: existingGrade.graded_at
        };
      }

      return responseData;
    } catch (error) {
      logger.error('Error getting submission progress details:', error);
      throw error;
    }
  }

  /**
   * Get comprehensive grade statistics with student details
   */
  async getComprehensiveGradeStatistics(query, user) {
    try {
      const {
        courseId,
        projectId,
        search,
        page = 1,
        limit = 10,
        sortBy = 'name',
        sortOrder = 'asc'
      } = query;

      // Build where clause for filtering
      let whereClause = {};
      let courseFilter = {};
      let userFilter = {};

      if (courseId) {
        courseFilter.id = courseId;
      }

      if (projectId) {
        whereClause.project_id = projectId;
      }

      // Add search functionality
      if (search) {
        userFilter[Op.or] = [
          { name: { [Op.iLike]: `%${search}%` } },
          { email: { [Op.iLike]: `%${search}%` } }
        ];
      }

      // Get all students with their submissions and progress
      const students = await User.findAll({
        where: userFilter,
        include: [
          {
            model: LtiContextEnrollment,
            as: 'enrollments',
            where: { role_in_course: 'student', enrollment_status: 'active' },
            include: [
              {
                model: LtiContext,
                as: 'course',
                where: courseFilter,
                attributes: [
                  'id',
                  ['context_title', 'name'],
                  ['context_label', 'code']
                ],
                raw: true,
                include: [
                  {
                    model: Project,
                    as: 'projects',
                    where: projectId ? { id: projectId } : {},
                    include: [
                      {
                        model: Checkpoint,
                        as: 'checkpoints',
                        include: [
                          {
                            model: Rubric,
                            as: 'rubrics'
                          }
                        ]
                      },
                      {
                        model: Submission,
                        as: 'submissions',
                        required: false,
                        include: [
                          {
                            model: Grade,
                            as: 'grade',
                            required: false
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ],
        attributes: ['id', 'name', 'email']
      });

      const studentDetails = [];
      let totalStudents = 0;
      let totalPendingGrading = 0;
      let totalCompletedGrading = 0;

      for (const student of students) {
        totalStudents++;
        let studentTotalRubrics = 0;
        let studentCompletedRubrics = 0;
        let studentPendingRubrics = 0;
        let latestSubmissionDate = null;
        let hasSubmissions = false;

        for (const enrollment of student.enrollments) {
          for (const course of enrollment.course ? [enrollment.course] : []) {
            for (const project of course.projects || []) {
              // Count total rubrics for this project
              const projectRubricCount = project.checkpoints.reduce(
                (sum, checkpoint) => {
                  return sum + (checkpoint.rubrics?.length || 0);
                },
                0
              );
              studentTotalRubrics += projectRubricCount;

              // Find student's submission for this project
              const studentSubmission = project.submissions?.find(
                sub => sub.user_id === student.id
              );

              if (studentSubmission) {
                hasSubmissions = true;

                // Update latest submission date
                if (studentSubmission.submitted_at) {
                  const submissionDate = new Date(
                    studentSubmission.submitted_at
                  );
                  if (
                    !latestSubmissionDate ||
                    submissionDate > latestSubmissionDate
                  ) {
                    latestSubmissionDate = submissionDate;
                  }
                }

                // Check if graded
                if (studentSubmission.grade) {
                  studentCompletedRubrics += projectRubricCount;
                } else {
                  studentPendingRubrics += projectRubricCount;
                }
              } else {
                studentPendingRubrics += projectRubricCount;
              }
            }
          }
        }

        // Update overall counters
        if (studentPendingRubrics > 0) {
          totalPendingGrading++;
        }
        if (studentCompletedRubrics > 0) {
          totalCompletedGrading++;
        }

        studentDetails.push({
          id: student.id,
          name: student.name,
          email: student.email,
          totalRubricCount: studentTotalRubrics,
          completedCount: studentCompletedRubrics,
          pendingCount: studentPendingRubrics,
          completionPercentage:
            studentTotalRubrics > 0
              ? Math.round(
                  (studentCompletedRubrics / studentTotalRubrics) * 100
                )
              : 0,
          latestSubmissionDate: latestSubmissionDate,
          hasSubmissions
        });
      }

      // Sort student details
      studentDetails.sort((a, b) => {
        let aValue = a[sortBy];
        let bValue = b[sortBy];

        // Handle date sorting
        if (sortBy === 'latestSubmissionDate') {
          aValue = aValue ? new Date(aValue) : new Date(0);
          bValue = bValue ? new Date(bValue) : new Date(0);
        }

        // Handle string sorting
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          aValue = aValue.toLowerCase();
          bValue = bValue.toLowerCase();
        }

        if (sortOrder === 'desc') {
          return bValue > aValue ? 1 : bValue < aValue ? -1 : 0;
        } else {
          return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
        }
      });

      // Apply pagination
      const totalItems = studentDetails.length;
      const totalPages = Math.ceil(totalItems / limit);
      const offset = (page - 1) * limit;
      const paginatedStudents = studentDetails.slice(offset, offset + limit);

      // Calculate overall completion percentage
      const overallCompletionPercentage =
        totalStudents > 0
          ? Math.round((totalCompletedGrading / totalStudents) * 100)
          : 0;

      return {
        overview: {
          totalStudentCount: totalStudents,
          pendingGrading: totalPendingGrading,
          completedGrading: totalCompletedGrading,
          completionPercentage: overallCompletionPercentage
        },
        students: paginatedStudents,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems,
          itemsPerPage: limit,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        },
        filters: {
          courseId: courseId || null,
          projectId: projectId || null,
          search: search || null,
          sortBy,
          sortOrder
        },
        generatedAt: new Date()
      };
    } catch (error) {
      logger.error('Error getting comprehensive grade statistics:', error);
      throw error;
    }
  }

  /**
   * Get detailed student information
   */
  async getStudentDetails(studentId, user) {
    try {
      // Get student with all related information
      const student = await User.findByPk(studentId, {
        include: [
          {
            model: LtiContextEnrollment,
            as: 'enrollments',
            where: { role_in_course: 'student' },
            include: [
              {
                model: LtiContext,
                as: 'course',
                attributes: [
                  'id',
                  ['context_title', 'name'],
                  ['context_label', 'code']
                ],
                raw: true,
                include: [
                  {
                    model: Project,
                    as: 'projects',
                    include: [
                      {
                        model: Submission,
                        as: 'submissions',
                        where: { user_id: studentId },
                        required: false,
                        include: [
                          {
                            model: Grade,
                            as: 'grade',
                            required: false
                          }
                        ]
                      },
                      {
                        model: Checkpoint,
                        as: 'checkpoints',
                        include: [
                          {
                            model: Rubric,
                            as: 'rubrics'
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ],
        attributes: [
          'id',
          'name',
          'email',
          'role',
          'status',
          'last_login',
          'created_at',
          'updated_at'
        ]
      });

      if (!student) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Student not found');
      }

      // Process course and project details
      const courseDetails = [];
      let totalProjects = 0;
      let completedProjects = 0;
      let totalRubrics = 0;
      let completedRubrics = 0;

      for (const enrollment of student.enrollments) {
        const course = enrollment.course;
        const projects = [];

        for (const project of course.projects || []) {
          totalProjects++;

          // Count rubrics for this project
          const projectRubricCount = project.checkpoints.reduce(
            (sum, checkpoint) => {
              return sum + (checkpoint.rubrics?.length || 0);
            },
            0
          );
          totalRubrics += projectRubricCount;

          // Find student's submission
          const submission = project.submissions?.[0];
          const isCompleted = submission?.grade ? true : false;

          if (isCompleted) {
            completedProjects++;
            completedRubrics += projectRubricCount;
          }

          projects.push({
            id: project.id,
            title: project.title,
            description: project.description,
            dueDate: project.due_date,
            status: project.status,
            difficultyLevel: project.difficulty_level,
            maxAttempts: project.max_attempts,
            rubricCount: projectRubricCount,
            submission: submission
              ? {
                  id: submission.id,
                  status: submission.status,
                  submittedAt: submission.submitted_at,
                  attempts: submission.attempts,
                  grade: submission.grade
                    ? {
                        id: submission.grade.id,
                        totalScore: submission.grade.total_score,
                        maxScore: submission.grade.max_score,
                        percentage: submission.grade.percentage,
                        letterGrade: submission.grade.letter_grade,
                        feedback: submission.grade.feedback,
                        gradedAt: submission.grade.graded_at
                      }
                    : null
                }
              : null,
            checkpoints: project.checkpoints.map(checkpoint => ({
              id: checkpoint.id,
              title: checkpoint.title,
              checkpointNumber: checkpoint.checkpoint_number,
              dueDate: checkpoint.due_date,
              isRequired: checkpoint.is_required,
              rubrics: checkpoint.rubrics.map(rubric => ({
                id: rubric.id,
                title: rubric.title,
                totalPoints: rubric.total_points,
                criteriaCount: rubric.criteria?.length || 0
              }))
            }))
          });
        }

        courseDetails.push({
          id: course.id,
          name: course.name,
          code: course.code,
          description: course.description,
          enrollmentStatus: enrollment.enrollment_status,
          enrolledAt: enrollment.enrolled_at,
          roleInCourse: enrollment.role_in_course,
          projects
        });
      }

      return {
        student: {
          id: student.id,
          name: student.name,
          email: student.email,
          role: student.role,
          status: student.status,
          lastLogin: student.last_login,
          createdAt: student.created_at,
          updatedAt: student.updated_at
        },
        statistics: {
          totalCourses: courseDetails.length,
          totalProjects,
          completedProjects,
          projectCompletionPercentage:
            totalProjects > 0
              ? Math.round((completedProjects / totalProjects) * 100)
              : 0,
          totalRubrics,
          completedRubrics,
          rubricCompletionPercentage:
            totalRubrics > 0
              ? Math.round((completedRubrics / totalRubrics) * 100)
              : 0
        },
        courses: courseDetails,
        generatedAt: new Date()
      };
    } catch (error) {
      logger.error('Error getting student details:', error);
      throw error;
    }
  }

  /**
   * Calculate letter grade from percentage
   */
  calculateLetterGrade(percentage) {
    if (percentage >= 90) return 'A+';
    if (percentage >= 85) return 'A';
    if (percentage >= 80) return 'A-';
    if (percentage >= 75) return 'B+';
    if (percentage >= 70) return 'B';
    if (percentage >= 65) return 'B-';
    if (percentage >= 60) return 'C+';
    if (percentage >= 55) return 'C';
    if (percentage >= 50) return 'C-';
    if (percentage >= 45) return 'D';
    return 'F';
  }
}

export default new GradeService();
