import {
  StudentProjectProgress,
  StudentActivity,
  Project,
  LtiContext,
  Checkpoint,
  CheckpointProgress,
  User
} from '../models/associations.js';
import { Op } from 'sequelize';
import logger from '../config/logger.config.js';

class StudentDashboardService {
  /**
   * Get comprehensive student dashboard
   */
  async getStudentDashboard(studentId, courseId = null) {
    try {
      // Get project overview
      const projectOverview = await this.getStudentProjectOverview(
        studentId,
        courseId
      );

      // Get upcoming deadlines
      const upcomingDeadlines = await this.getUpcomingDeadlines(studentId, 30);

      // Get recent activity
      const recentActivity = await this.getRecentActivity(studentId, 10);

      // Get course progress summary
      const courseProgress = await this.getCourseProgressSummary(
        studentId,
        courseId
      );

      return {
        studentId,
        projectOverview,
        upcomingDeadlines,
        recentActivity,
        courseProgress,
        lastUpdated: new Date()
      };
    } catch (error) {
      logger.error('Error getting student dashboard:', error);
      throw error;
    }
  }

  /**
   * Get student's project overview with progress
   */
  async getStudentProjectOverview(studentId, courseId = null) {
    try {
      const whereClause = { student_id: studentId };

      if (courseId) {
        whereClause.course_id = courseId;
      }

      const progress = await StudentProjectProgress.findAll({
        where: whereClause,
        include: [
          {
            model: Project,
            as: 'project',
            attributes: [
              'id',
              'title',
              'description',
              'due_date',
              'status',
              'difficulty_level',
              'estimated_hours',
              'start_date',
              'end_date'
            ]
          },
          {
            model: LtiContext,
            as: 'course',
            attributes: [
              'id',
              ['context_title', 'name'],
              ['context_label', 'code']
            ],
            raw: true
          }
        ],
        order: [['last_activity', 'DESC']]
      });

      const overview = {
        totalProjects: progress.length,
        activeProjects: progress.filter(p => p.status === 'in_progress').length,
        completedProjects: progress.filter(p => p.status === 'completed')
          .length,
        overdueProjects: progress.filter(p => p.status === 'overdue').length,
        averageProgress:
          progress.length > 0
            ? progress.reduce(
                (sum, p) => sum + parseFloat(p.progress_percentage),
                0
              ) / progress.length
            : 0,
        projects: progress.map(p => ({
          id: p.project.id,
          title: p.project.title,
          course: p.course,
          status: p.status,
          progress: p.progress_percentage,
          dueDate: p.project.due_date,
          startDate: p.project.start_date,
          endDate: p.project.end_date,
          lastActivity: p.last_activity,
          timeSpent: p.time_spent_hours,
          grade: p.grade,
          difficultyLevel: p.project.difficulty_level,
          estimatedHours: p.project.estimated_hours
        }))
      };

      return overview;
    } catch (error) {
      logger.error('Error getting student project overview:', error);
      throw error;
    }
  }

  /**
   * Get project progress data
   */
  async getProjectProgress(studentId, projectId) {
    try {
      const progress = await StudentProjectProgress.findOne({
        where: { student_id: studentId, project_id: projectId },
        include: [
          {
            model: Project,
            as: 'project',
            include: [
              {
                model: Checkpoint,
                as: 'checkpoints',
                include: [
                  {
                    model: CheckpointProgress,
                    as: 'studentProgress',
                    where: { student_id: studentId },
                    required: false
                  }
                ]
              }
            ]
          }
        ]
      });

      if (!progress) {
        throw new Error('Project progress not found');
      }

      // Calculate checkpoint progress
      const checkpointProgress = progress.project.checkpoints.map(
        checkpoint => {
          const studentProgress = checkpoint.studentProgress[0];
          return {
            id: checkpoint.id,
            title: checkpoint.title,
            description: checkpoint.description,
            dueDate: checkpoint.due_date,
            status: studentProgress ? studentProgress.status : 'not_started',
            submittedAt: studentProgress ? studentProgress.submitted_at : null,
            grade: studentProgress ? studentProgress.grade : null,
            feedback: studentProgress ? studentProgress.feedback : null
          };
        }
      );

      return {
        projectId: progress.project_id,
        projectTitle: progress.project.title,
        status: progress.status,
        progressPercentage: progress.progress_percentage,
        currentPhase: progress.current_phase,
        timeSpent: progress.time_spent_hours,
        startDate: progress.start_date,
        completionDate: progress.completion_date,
        grade: progress.grade,
        feedback: progress.feedback,
        checkpoints: checkpointProgress,
        lastActivity: progress.last_activity,
        projectDetails: {
          dueDate: progress.project.due_date,
          difficultyLevel: progress.project.difficulty_level,
          estimatedHours: progress.project.estimated_hours
        }
      };
    } catch (error) {
      logger.error('Error getting project progress:', error);
      throw error;
    }
  }

  /**
   * Get upcoming deadlines
   */
  async getUpcomingDeadlines(studentId, days = 30) {
    try {
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + days);

      const deadlines = await StudentProjectProgress.findAll({
        where: {
          student_id: studentId,
          status: { [Op.in]: ['not_started', 'in_progress'] }
        },
        include: [
          {
            model: Project,
            as: 'project',
            where: {
              due_date: {
                [Op.between]: [new Date(), endDate]
              }
            },
            attributes: ['id', 'title', 'due_date']
          },
          {
            model: LtiContext,
            as: 'course',
            attributes: [
              'id',
              ['context_title', 'name'],
              ['context_label', 'code']
            ],
            raw: true
          }
        ],
        order: [['project', 'due_date', 'ASC']]
      });

      return deadlines.map(deadline => ({
        projectId: deadline.project.id,
        projectTitle: deadline.project.title,
        course: deadline.course,
        dueDate: deadline.project.due_date,
        daysUntilDue: Math.ceil(
          (deadline.project.due_date - new Date()) / (1000 * 60 * 60 * 24)
        ),
        status: deadline.status,
        progress: deadline.progress_percentage
      }));
    } catch (error) {
      logger.error('Error getting upcoming deadlines:', error);
      throw error;
    }
  }

  /**
   * Get recent activity
   */
  async getRecentActivity(studentId, limit = 10, activityType = null) {
    try {
      const whereClause = { student_id: studentId };

      if (activityType) {
        whereClause.activity_type = activityType;
      }

      const activities = await StudentActivity.findAll({
        where: whereClause,
        include: [
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'title'],
            required: false
          },
          {
            model: LtiContext,
            as: 'course',
            attributes: [
              'id',
              ['context_title', 'name'],
              ['context_label', 'code']
            ],
            raw: true,
            required: false
          }
        ],
        order: [['timestamp', 'DESC']],
        limit: parseInt(limit)
      });

      return activities.map(activity => ({
        id: activity.id,
        type: activity.activity_type,
        title: activity.title,
        description: activity.description,
        timestamp: activity.timestamp,
        isRead: activity.is_read,
        project: activity.project,
        course: activity.course,
        metadata: activity.metadata
      }));
    } catch (error) {
      logger.error('Error getting recent activity:', error);
      throw error;
    }
  }

  /**
   * Get course progress summary
   */
  async getCourseProgressSummary(studentId, courseId = null) {
    try {
      const whereClause = { student_id: studentId };

      if (courseId) {
        whereClause.course_id = courseId;
      }

      const progress = await StudentProjectProgress.findAll({
        where: whereClause,
        include: [
          {
            model: LtiContext,
            as: 'course',
            attributes: [
              'id',
              ['context_title', 'name'],
              ['context_label', 'code']
            ],
            raw: true
          }
        ]
      });

      // Group by course
      const courseSummary = {};

      progress.forEach(p => {
        const courseKey = p.course.id;
        if (!courseSummary[courseKey]) {
          courseSummary[courseKey] = {
            courseId: p.course.id,
            courseName: p.course.name,
            courseCode: p.course.code,
            totalProjects: 0,
            completedProjects: 0,
            activeProjects: 0,
            averageProgress: 0,
            totalTimeSpent: 0
          };
        }

        courseSummary[courseKey].totalProjects++;
        if (p.status === 'completed')
          courseSummary[courseKey].completedProjects++;
        if (p.status === 'in_progress')
          courseSummary[courseKey].activeProjects++;
        courseSummary[courseKey].totalTimeSpent += parseFloat(
          p.time_spent_hours || 0
        );
      });

      // Calculate averages
      Object.values(courseSummary).forEach(course => {
        const courseProgress = progress.filter(
          p => p.course_id === course.courseId
        );
        course.averageProgress =
          courseProgress.length > 0
            ? courseProgress.reduce(
                (sum, p) => sum + parseFloat(p.progress_percentage),
                0
              ) / courseProgress.length
            : 0;
      });

      return Object.values(courseSummary);
    } catch (error) {
      logger.error('Error getting course progress summary:', error);
      throw error;
    }
  }

  /**
   * Create or update student project progress
   */
  async updateProjectProgress(studentId, projectId, courseId, progressData) {
    try {
      const [progress, created] = await StudentProjectProgress.findOrCreate({
        where: { student_id: studentId, project_id: projectId },
        defaults: {
          course_id: courseId,
          enrollment_date: new Date(),
          ...progressData
        }
      });

      if (!created) {
        await progress.update(progressData);
      }

      // Log activity
      await this.logActivity(
        studentId,
        projectId,
        courseId,
        'project_started',
        'Project Progress Updated',
        'Your project progress has been updated'
      );

      return progress;
    } catch (error) {
      logger.error('Error updating project progress:', error);
      throw error;
    }
  }

  /**
   * Log student activity
   */
  async logActivity(
    studentId,
    projectId = null,
    courseId = null,
    activityType,
    title,
    description = null,
    metadata = {}
  ) {
    try {
      const activity = await StudentActivity.create({
        student_id: studentId,
        project_id: projectId,
        course_id: courseId,
        activity_type: activityType,
        title,
        description,
        metadata,
        timestamp: new Date()
      });

      logger.info(
        `Student activity logged: ${activityType} for student ${studentId}`
      );
      return activity;
    } catch (error) {
      logger.error('Error logging student activity:', error);
      // Don't throw error for activity logging failures
    }
  }

  /**
   * Mark activity as read
   */
  async markActivityAsRead(activityId, studentId) {
    try {
      const activity = await StudentActivity.findOne({
        where: { id: activityId, student_id: studentId }
      });

      if (!activity) {
        throw new Error('Activity not found');
      }

      await activity.update({ is_read: true });
      return activity;
    } catch (error) {
      logger.error('Error marking activity as read:', error);
      throw error;
    }
  }

  /**
   * Get unread activity count
   */
  async getUnreadActivityCount(studentId) {
    try {
      const count = await StudentActivity.count({
        where: {
          student_id: studentId,
          is_read: false
        }
      });

      return count;
    } catch (error) {
      logger.error('Error getting unread activity count:', error);
      return 0;
    }
  }

  /**
   * Get student project statistics
   */
  async getStudentProjectStats(studentId, courseId = null) {
    try {
      const whereClause = { student_id: studentId };

      if (courseId) {
        whereClause.course_id = courseId;
      }

      const stats = await StudentProjectProgress.findAll({
        where: whereClause,
        attributes: [
          'status',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['status']
      });

      const totalProjects = await StudentProjectProgress.count({
        where: whereClause
      });

      const totalTimeSpent = await StudentProjectProgress.sum(
        'time_spent_hours',
        {
          where: whereClause
        }
      );

      return {
        totalProjects,
        totalTimeSpent: totalTimeSpent || 0,
        byStatus: stats.reduce((acc, stat) => {
          acc[stat.status] = parseInt(stat.dataValues.count);
          return acc;
        }, {}),
        statuses: stats.map(stat => stat.status)
      };
    } catch (error) {
      logger.error('Error getting student project stats:', error);
      return {
        totalProjects: 0,
        totalTimeSpent: 0,
        byStatus: {},
        statuses: []
      };
    }
  }
}

export default new StudentDashboardService();
