import httpStatus from 'http-status';
import { Op } from 'sequelize';
import {
  Checkpoint,
  LtiContext,
  LtiContextEnrollment,
  Grade,
  Project,
  Submission,
  User
} from '../models/associations.js';
import ApiError from '../utils/ApiError.utils.js';
import { LoggerError } from '../utils/helpers.utils.js';
import { default as S3Service } from './s3.service.js';

class SubmissionService {
  //     /**
  //      * Get submissions with filters and pagination
  //      */
  static async getSubmissions(req) {
    try {
      const {
        page = 1,
        limit = 10,
        projectId,
        status,
        sortBy = 'submitted_at',
        sortOrder = 'desc'
      } = req.query;

      // console.log('request details \n:',req);

      const userId = req.user.id;

      const pageNum = parseInt(page, 10);
      const limitNum = parseInt(limit, 10);
      const offset = (pageNum - 1) * limitNum;

      const whereClause = {};

      // Role-based access
      if (req.userRoles?.includes('admin')) {
        // Admin can see everything (no restrictions)
        if (projectId) whereClause.project_id = projectId;
        if (userId) whereClause.user_id = userId;
      } else if (req.userRoles?.includes('instructor')) {
        // Instructor: Get only submissions for projects owned by them
        const instructorProjects = await Project.findAll({
          where: {
            instructor_id: { [Op.contains]: [userId] }
          },
          attributes: ['id'],
          raw: true
        });
        console.log('Instructor Proj', instructorProjects);
        // Extract project IDs into a plain array
        const instructorProjectIds = instructorProjects.map(p => p.id);

        if (instructorProjectIds.length === 0) {
          whereClause.id = null; // Force query to return nothing
        } else if (projectId) {
          if (instructorProjectIds.includes(projectId)) {
            whereClause.project_id = projectId;
          } else {
            throw new ApiError(
              httpStatus.FORBIDDEN,
              'Access to this project is denied'
            );
          }
        } else {
          whereClause.project_id = { [Op.in]: instructorProjectIds };
        }
      } else if (req.userRoles?.includes('student')) {
        // Student: Only see their own submissions
        whereClause.user_id = req.user.id;
        console.log('Final whereClause:', whereClause);

        if (projectId) whereClause.project_id = projectId; // student filter still applies
      } else {
        throw new ApiError(httpStatus.FORBIDDEN, 'Unauthorized access');
      }

      if (status && status !== 'in_progress') {
        whereClause.status = status;
      }

      const { count, rows } = await Submission.findAndCountAll({
        where: whereClause,
        include: this.getSubmissionIncludes(),
        limit: limitNum,
        offset,
        order: [[sortBy, sortOrder.toUpperCase()]]
      });

      return {
        submissions: this.transformSubmissions(rows),
        pagination: {
          currentPage: pageNum,
          totalPages: Math.ceil(count / parseInt(limit)),
          totalItems: count,
          itemsPerPage: limitNum
        }
      };
    } catch (error) {
      console.error('Error fetching submissions:', error);
      throw new ApiError(
        httpStatus.INTERNAL_SERVER_ERROR,
        'Failed to fetch submissions'
      );
    }
  }

  //     /**
  //      * Get submission by ID
  //      */
  static async getSubmissionById(req) {
    try {
      const { id } = req.params;
      console.log('request details \n:', req.userRoles?.includes('instructor'));
      const submission = await Submission.findByPk(id, {
        include: this.getSubmissionIncludes()
      });

      if (!submission) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Submission not found');
      }

      const isAdmin = req.userRoles?.includes('admin');
      const isOwner = submission.user_id === req.user.id;
      // const isInstructor = submission.project?.course?.instructor_id === req.user.id;
      const isInstructor = req.userRoles?.includes('instructor');
      const hasAccess = isAdmin || isOwner || isInstructor;

      if (!hasAccess) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'You do not have permission to view this submission'
        );
      }

      let notebookPresignedUrls = [];
      if (submission.metadata?.notebooks?.length) {
        try {
          notebookPresignedUrls = await Promise.all(
            submission.metadata.notebooks.map(async nb => {
              try {
                const url = await S3Service.generatePresignedDownloadUrl(
                  nb.key,
                  3600
                );
                return { ...nb, presignedUrl: url };
              } catch (err) {
                LoggerError(
                  req,
                  `Error generating presigned URL for ${nb.key}`,
                  'getSubmissionById',
                  httpStatus.INTERNAL_SERVER_ERROR,
                  err
                );
                return { ...nb, presignedUrl: null };
              }
            })
          );
        } catch (error) {
          LoggerError(
            req,
            'Error generating presigned URL',
            'getSubmissionById',
            httpStatus.INTERNAL_SERVER_ERROR,
            error
          );
        }
      }

      return {
        ...this.transformSubmission(submission),
        notebookPresignedUrls
      };
    } catch (error) {
      // Ensure standardized error handling
      if (error instanceof ApiError) throw error;

      LoggerError(
        req,
        'Unexpected error in getSubmissionById',
        'getSubmissionById',
        httpStatus.INTERNAL_SERVER_ERROR,
        error
      );

      throw new ApiError(
        httpStatus.INTERNAL_SERVER_ERROR,
        'Failed to retrieve submission'
      );
    }
  }

  /**
   * Get submission by Instructor ID
   */
  static async getSubmissionByInstructorId(req) {
    try {
      // 1. Get instructor user ID from session
      const instructorId = req.session?.user?.id || req.user?.id;
      if (!instructorId) {
        throw new ApiError(
          httpStatus.UNAUTHORIZED,
          'Instructor ID not found in session'
        );
      }

      // 2. Get pagination and filter parameters from query
      const {
        page = 1,
        limit = 10,
        status,
        sortBy = 'submitted_at',
        sortOrder = 'desc'
      } = req.query;
      const pageNum = parseInt(page, 10);
      const limitNum = parseInt(limit, 10);
      const offset = (pageNum - 1) * limitNum;

      // 3. Get all project IDs associated with this instructor using the efficient array operator
      const instructorProjects = await Project.findAll({
        where: {
          instructor_id: { [Op.contains]: [instructorId] }
        },
        attributes: ['id'],
        raw: true
      });

      // Since Project.findAll always returns an array, we can map it directly.
      const projectIds = instructorProjects.map(project => project.id);

      // 4. Handle the case where the instructor has no projects, exiting early.
      if (projectIds.length === 0) {
        return {
          submissions: [],
          pagination: {
            currentPage: pageNum,
            totalPages: 0,
            totalItems: 0,
            itemsPerPage: limitNum
          },
          instructorInfo: { instructorId, totalProjects: 0 }
        };
      }

      // 5. Build the where clause for the submissions query
      const whereClause = { project_id: projectIds };
      if (status) {
        whereClause.status = status;
      }

      // 6. Get the paginated list of submissions and the total count
      const { count, rows: submissions } = await Submission.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['id', 'name', 'email', 'profile_picture']
          },
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'title', 'due_date', 'difficulty_level'],
            include: [
              {
                model: LtiContext,
                as: 'course',
                attributes: [
                  'id',
                  ['context_title', 'name'],
                  ['context_label', 'code']
                ],
                raw: true
              }
            ]
          },
          {
            model: Grade,
            as: 'grade',
            required: false, // Use LEFT JOIN to include submissions without grades
            include: [
              {
                model: User,
                as: 'evaluator',
                attributes: ['id', 'name', 'email']
              }
            ]
          }
        ],
        limit: limitNum,
        offset,
        order: [[sortBy, sortOrder.toUpperCase()]],
        distinct: true // Important for correct counts with JOINs
      });

      // 7. Calculate submission statistics by status
      const statusCounts = await Submission.findAll({
        where: { project_id: projectIds },
        attributes: [
          'status',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['status'],
        raw: true
      });

      // Use reduce for a clean way to transform the status counts array into an object
      const statusStats = statusCounts.reduce((stats, item) => {
        stats[item.status] = parseInt(item.count, 10);
        return stats;
      }, {});

      const transformedSubmissions = this.transformSubmissions(submissions);

      // 8. Return the final, comprehensive response object
      return {
        submissions: transformedSubmissions,
        pagination: {
          currentPage: pageNum,
          totalPages: Math.ceil(count / limitNum),
          totalItems: count,
          itemsPerPage: limitNum
        },
        instructorInfo: {
          instructorId,
          totalProjects: projectIds.length,
          projectIds
        },
        statistics: {
          totalSubmissions: count,
          statusBreakdown: statusStats,
          ...statusStats
        }
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Create or update submission
   */
  static async createOrUpdateSubmission(req) {
    try {
      const {
        projectId,
        checkpointId,
        submissionSummary,
        metadata = {},
        timeSpent,
        max_attempts
      } = req.body;

      const userId = req.user.id;

      // Validate required fields
      if (!projectId || !userId || !checkpointId) {
        console.log('ProjectId', projectId);
        console.log('UserId', userId);
        console.log('CheckpointId', checkpointId);
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          'Project ID, Checkpoint ID and User ID are required'
        );
      }

      if (req.userRoles?.includes('instructor')) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'Instructors cannot create submissions'
        );
      }

      if (req.userRoles?.includes('student')) {
        const project = await this.validateProjectAccess(projectId, userId);
        if (!project)
          throw new ApiError(
            httpStatus.NOT_FOUND,
            'Project not found or access denied'
          );
      }

      // Fetch checkpoint info
      const currentCheckpoint = await Checkpoint.findOne({
        where: { id: checkpointId, project_id: projectId },
        raw: true
      });

      if (!currentCheckpoint) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Checkpoint not found');
      }

      // ✅ Get all required checkpoints for this project
      const requiredCheckpoints = await Checkpoint.findAll({
        where: { project_id: projectId, is_required: true },
        order: [['checkpoint_number', 'ASC']],
        raw: true
      });

      if (requiredCheckpoints.length > 0) {
        // Find previous required checkpoints
        const previousRequired = requiredCheckpoints.filter(
          cp => cp.checkpoint_number < currentCheckpoint.checkpoint_number
        );

        if (previousRequired.length > 0) {
          // Get progress of student on those checkpoints
          const progressRecords = await CheckpointProgress.findAll({
            where: {
              checkpoint_id: { [Op.in]: previousRequired.map(c => c.id) },
              user_id: userId,
              project_id: projectId
            },
            raw: true
          });

          // Check if all required previous checkpoints are submitted
          const incompleteRequired = previousRequired.filter(reqCP => {
            const progress = progressRecords.find(
              p => p.checkpoint_id === reqCP.id
            );
            return (
              !progress ||
              !['submitted', 'reviewed', 'completed'].includes(progress.status)
            );
          });

          if (incompleteRequired.length > 0) {
            throw new ApiError(
              httpStatus.FORBIDDEN,
              `You must complete required checkpoint "${incompleteRequired[0].title}" before submitting this checkpoint`
            );
          }
        }
      }

      // Find existing submission
      let submission = await Submission.findOne({
        where: { project_id: projectId, user_id: userId } //, checkpoint_id: checkpointId }
      });

      if (max_attempts != null && max_attempts > 0) {
        if (submission && submission.attempts >= max_attempts) {
          throw new ApiError(
            httpStatus.FORBIDDEN,
            `You have reached the maximum of ${max_attempts} submission attempts`
          );
        }
      }

      let uploadedFiles = [];

      // Handle uploaded file(s) if any
      if (req.files?.length > 0) {
        uploadedFiles = await Promise.all(
          req.files.map(file =>
            S3Service.uploadFile(file, 'submission', userId, {
              projectId,
              checkpointId
            })
          )
        );

        // Delete old files if resubmitting
        if (submission?.files?.length) {
          await Promise.all(
            submission.files.map(async oldFile => {
              const oldKey = S3Service.extractKeyFromUrl(oldFile.key);
              if (oldKey) await S3Service.deleteFile(oldKey);
            })
          );
        }
      }

      // Separate notebooks and other files
      const notebooks = uploadedFiles.filter(f => f.key.endsWith('.ipynb'));
      const otherFiles = uploadedFiles.filter(f => !f.key.endsWith('.ipynb'));

      const submissionData = {
        project_id: projectId,
        // checkpoint_id: checkpointId,
        user_id: userId,
        status: 'submitted',
        submitted_at: new Date(),
        submission_summary: submissionSummary,
        metadata: {
          ...(submission?.metadata || {}),
          ...metadata,
          files: otherFiles,
          notebooks: notebooks
        },
        updated_at: new Date(),
        time_spent: timeSpent ?? submission?.time_spent ?? 0,
        current_progress:
          metadata?.progress ?? submission?.current_progress ?? 0
      };

      // Update or create submission
      submission = submission
        ? await submission.update({
            ...submissionData,
            attempts: (submission.attempts || 0) + 1
          })
        : await Submission.create({
            ...submissionData,
            attempts: 1,
            created_at: new Date(),
            updated_at: new Date()
          });

      return {
        id: submission.id,
        status: submission.status,
        projectId: submission.project_id,
        userId: submission.user_id,
        checkpointId: submission.checkpoint_id,
        submissionSummary: submission.submission_summary,
        metadata: submission.metadata,
        submittedAt: submission.submitted_at,
        updatedAt: submission.updated_at,
        timeSpent: submission.time_spent,
        attempts: submission.attempts,
        currentProgress: submission.current_progress
      };
    } catch (error) {
      console.error('createOrUpdateSubmission Error:', error);
      if (error instanceof ApiError) throw error;
      throw new ApiError(
        httpStatus.INTERNAL_SERVER_ERROR,
        `Error processing submission: ${error.message}`
      );
    }
  }
  //   /**
  //    * Submit final assignment
  //    */

  static async submitAssignment(req) {
    const { id } = req.params;

    const submission = await Submission.findByPk(id, {
      include: [
        {
          model: Project,
          as: 'project',
          include: [
            {
              model: LtiContext,
              as: 'course',
              attributes: [
                'id',
                ['context_title', 'name'],
                ['context_label', 'code']
              ],
              raw: true
            }
          ]
        }
      ]
    });

    this.validateSubmission(submission, req.user.id);

    const timeSpent = this.calculateTimeSpent(submission);
    const attempts = (submission.attempts || 0) + 1;
    const finalMetadata = {
      ...(submission.metadata || {}),
      ...this.buildFinalSubmissionMetadata(submission)
    };

    const updatedSubmission = await submission.update({
      status: 'submitted',
      submitted_at: new Date(),
      time_spent: timeSpent,
      attempts,
      metadata: finalMetadata
    });

    const result = {
      id: updatedSubmission.id,
      status: updatedSubmission.status,
      submittedAt: updatedSubmission.submitted_at,
      timeSpent: updatedSubmission.time_spent,
      attempts: updatedSubmission.attempts,
      projectTitle: submission.project?.title,
      courseName: submission.project?.course?.name,
      notebooks: updatedSubmission.metadata?.notebooks || [],
      files: updatedSubmission.metadata?.files || []
    };

    return result;
  }

  static async downloadSubmissionNotebook(req) {
    try {
      const { id } = req.params;

      const submission = await Submission.findByPk(id, {
        include: [
          {
            model: Project,
            as: 'project',
            include: [
              {
                model: LtiContext,
                as: 'course',
                attributes: [
                  'id',
                  ['context_title', 'name'],
                  ['context_label', 'code']
                ],
                raw: true
              }
            ]
          },
          { model: User, as: 'user', attributes: ['id', 'name', 'email'] }
        ]
      });

      if (!submission) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Submission not found');
      }

      const isAdmin = req.userRoles?.includes('admin');
      const isOwner = submission.user_id === req.user.id;
      const isInstructor =
        submission.project?.course?.instructor_id === req.user.id;
      const hasAccess = isAdmin || isOwner || isInstructor;

      if (!hasAccess) {
        throw new ApiError(
          httpStatus.FORBIDDEN,
          'You do not have permission to download this submission'
        );
      }

      const notebooks = submission.metadata?.notebooks || [];
      if (!notebooks.length) {
        throw new ApiError(
          httpStatus.NOT_FOUND,
          'No notebook file(s) found for this submission'
        );
      }

      const selectedNotebook = notebooks[0];

      //Getting the download URL
      const presignedUrl = await S3Service.generatePresignedDownloadUrl(
        selectedNotebook.key,
        3600
      );

      //Sanitize filename for download
      const sanitizedName = submission.user.name.replace(/[^a-zA-Z0-9]/g, '_');
      const sanitizedTitle = submission.project.title.replace(
        /[^a-zA-Z0-9]/g,
        '_'
      );
      const fileName = `${sanitizedName}_${sanitizedTitle}_${submission.id}.ipynb`;

      return {
        downloadUrl: presignedUrl,
        fileName,
        expiresIn: 3600,
        submission: {
          id: submission.id,
          status: submission.status,
          projectTitle: submission.project.title,
          courseName: submission.project.course.name,
          studentName: submission.user.name,
          submittedAt: submission.submitted_at
        }
      };
    } catch (error) {
      if (error instanceof ApiError) throw error;

      LoggerError(
        req,
        'Unexpected error in downloadSubmissionNotebook',
        'downloadSubmissionNotebook',
        httpStatus.INTERNAL_SERVER_ERROR,
        error
      );

      throw new ApiError(
        httpStatus.INTERNAL_SERVER_ERROR,
        'Failed to download submission notebook'
      );
    }
  }

  //   /**
  //    * Get submission statistics
  //    */
  static async getSubmissionStatistics(req) {
    const { projectId } = req.params;

    const project = await Project.findByPk(projectId, {
      include: [
        {
          model: LtiContext,
          as: 'course',
          attributes: [
            'id',
            ['context_title', 'name'],
            ['context_label', 'code']
          ],
          raw: true
        }
      ]
    });

    if (!project) {
      throw new Error('Project not found');
    }

    const hasPermission =
      req.userRoles?.includes('admin') ||
      project.course.instructor_id === req.user.id;

    if (!hasPermission) {
      throw new Error(
        'You do not have permission to view submission statistics'
      );
    }

    return await this.calculateStatistics(projectId, project);
  }

  //     // Private helper methods
  static getSubmissionIncludes() {
    return [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'email', 'profile_picture']
      },
      {
        model: Project,
        as: 'project',
        attributes: ['id', 'title', 'due_date', 'difficulty_level'],
        include: [
          {
            model: LtiContext,
            as: 'course',
            attributes: [
              'id',
              ['context_title', 'name'],
              ['context_label', 'code']
            ],
            raw: true
          }
        ]
      },
      {
        model: Grade,
        as: 'grade',
        include: [
          {
            model: User,
            as: 'evaluator',
            attributes: ['id', 'name', 'email']
          }
        ]
      }
    ];
  }
  //     // Inside SubmissionService class

  static transformSubmissions(submissions) {
    return submissions.map(submission => this.transformSubmission(submission));
  }

  static transformSubmission(submission) {
    if (!submission) return null;
    return {
      id: submission.id,
      status: submission.status,
      submittedAt: submission.submitted_at,
      executionTime: submission.execution_time,
      timeSpent: submission.time_spent,
      attempts: submission.attempts,
      submissionSummary: submission.submission_summary,
      // notebooks: submission.metadata?.notebooks || [],
      // files: submission.metadata?.files || [],
      metadata: submission.metadata,
      user: submission.user
        ? {
            id: submission.user.id,
            name: submission.user.name,
            email: submission.user.email,
            profilePicture: submission.user.profile_picture
          }
        : null,
      project: submission.project
        ? {
            id: submission.project.id,
            title: submission.project.title,
            dueDate: submission.project.due_date,
            difficultyLevel: submission.project.difficulty_level,
            course: submission.project.course
              ? {
                  id: submission.project.course.id,
                  name: submission.project.course.name,
                  code: submission.project.course.code
                }
              : null
          }
        : null,
      grade: submission.grade
        ? {
            id: submission.grade.id,
            totalScore: submission.grade.total_score,
            maxScore: submission.grade.max_score,
            percentage: submission.grade.percentage,
            letterGrade: submission.grade.letter_grade,
            feedback: submission.grade.feedback,
            // evaluator: submission.grade.evaluator ? {
            //     id: submission.grade.evaluator.id,
            //     name: submission.grade.evaluator.name,
            //     email: submission.grade.evaluator.email
            // } : null,
            gradedAt: submission.grade.graded_at
          }
        : null,
      createdAt: submission.created_at,
      updatedAt: submission.updated_at
    };
  }
  static async validateProjectAccess(projectId, userId) {
    const project = await Project.findByPk(projectId, {
      include: [
        {
          model: LtiContext,
          as: 'course',
          attributes: [
            'id',
            ['context_title', 'name'],
            ['context_label', 'code']
          ],
          raw: true
        }
      ]
    });

    if (!project) throw new Error('Project not found');
    if (project.status !== 'published')
      throw new Error('Project is not published');

    // Check enrollment separately
    const enrollment = await LtiContextEnrollment.findOne({
      where: {
        user_id: userId,
        context_id: project.course_id
      },
      attributes: ['id', 'enrollment_status']
    });

    if (!enrollment) throw new Error('User is not enrolled in this course');

    if (project.due_date && new Date() > new Date(project.due_date)) {
      throw new Error('Submission deadline has passed');
    }

    return project;
  }

  static calculateTimeSpent(submission) {
    const lastSaved = submission.metadata?.lastAutoSave
      ? new Date(submission.metadata.lastAutoSave)
      : null;
    const currentTimeSpent = lastSaved
      ? (Date.now() - lastSaved.getTime()) / 1000
      : 0;
    return Math.round(submission.time_spent + currentTimeSpent);
  }

  static buildAutoSaveMetadata(submission, newMetadata) {
    return {
      ...submission.metadata,
      ...newMetadata,
      lastAutoSave: new Date(),
      autoSaveCount: (submission.metadata?.autoSaveCount || 0) + 1,
      lastModified: new Date()
    };
  }

  static validateSubmission(submission, userId) {
    if (!submission) throw new Error('Submission not found');
    if (submission.user_id !== userId) throw new Error('Access denied');
    if (submission.status === 'submitted')
      throw new Error('Assignment has already been submitted');
    if (!submission.notebook_s3_url)
      throw new Error('Please upload your notebook before submitting');
    if (
      submission.project.due_date &&
      new Date() > new Date(submission.project.due_date)
    ) {
      throw new Error('Submission deadline has passed');
    }
  }

  static buildFinalSubmissionMetadata(submission) {
    return {
      ...(submission.metadata || {}),

      // ✅ preserve uploaded files and notebooks
      files: submission.metadata?.files || [],
      notebooks: submission.metadata?.notebooks || [],
      finalSubmission: true,
      submissionComplete: true,
      completedAt: new Date(),
      lastModified: new Date(),
      submissionHistory: [
        ...(submission.metadata?.submissionHistory || []),
        {
          timestamp: new Date(),
          status: 'submitted',
          timeSpent: submission.time_spent,
          attempts: submission.attempts
        }
      ]
    };
  }

  static async calculateStatistics(projectId, project) {
    const totalEnrolled = await LtiContextEnrollment.count({
      where: {
        context_id: project.course_id,
        role_in_course: 'student',
        enrollment_status: 'active'
      }
    });

    const totalSubmissions = await Submission.count({
      where: { project_id: projectId }
    });

    return {
      overview: {
        totalEnrolled,
        totalSubmissions,
        submissionRate:
          totalEnrolled > 0
            ? ((totalSubmissions / totalEnrolled) * 100).toFixed(1)
            : 0
      },
      metadata: {
        projectTitle: project.title,
        courseName: project.course.name,
        dueDate: project.due_date,
        generatedAt: new Date()
      }
    };
  }
}

export default SubmissionService;
