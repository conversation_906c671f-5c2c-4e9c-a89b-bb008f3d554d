import { Announcement, LtiContext, User } from '../models/associations.js';
import { Op } from 'sequelize';
import logger from '../config/logger.config.js';

class AnnouncementService {
  
  /**
   * Create a new announcement
   */
  async createAnnouncement(announcementData) {
    try {
      const announcement = await Announcement.create(announcementData);
      
      logger.info(`Announcement created: ${announcement.title} by user ${announcementData.created_by}`);
      
      return announcement;
    } catch (error) {
      logger.error('Error creating announcement:', error);
      throw error;
    }
  }

  /**
   * Get announcements for a course with filtering
   */
  async getCourseAnnouncements(courseId, options = {}) {
    try {
      const {
        status = 'published',
        announcementType,
        priority,
        isPinned,
        page = 1,
        limit = 20,
        includeExpired = false
      } = options;

      const whereClause = { course_id: courseId };
      
      if (status !== 'all') {
        whereClause.status = status;
      }
      
      if (announcementType) {
        whereClause.announcement_type = announcementType;
      }
      
      if (priority) {
        whereClause.priority = priority;
      }
      
      if (isPinned !== undefined) {
        whereClause.is_pinned = isPinned;
      }

      // Handle expired announcements
      if (!includeExpired) {
        whereClause[Op.or] = [
          { expires_at: null },
          { expires_at: { [Op.gt]: new Date() } }
        ];
      }

      const offset = (page - 1) * limit;
      
      const { count, rows: announcements } = await Announcement.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'name', 'email']
          },
          {
            model: User,
            as: 'publisher',
            attributes: ['id', 'name', 'email'],
            required: false
          }
        ],
        order: [
          ['is_pinned', 'DESC'],
          ['priority', 'DESC'],
          ['created_at', 'DESC']
        ],
        limit,
        offset
      });

      return {
        announcements,
        pagination: {
          page,
          limit,
          total: count,
          pages: Math.ceil(count / limit)
        }
      };
    } catch (error) {
      logger.error('Error getting course announcements:', error);
      throw error;
    }
  }

  /**
   * Get announcement by ID with full details
   */
  async getAnnouncementById(announcementId, userId = null) {
    try {
      const announcement = await Announcement.findByPk(announcementId, {
        include: [
          {
            model: LtiContext,
            as: 'course',
            attributes: [
              'id',
              ['context_title', 'name'],
              ['context_label', 'code'],
            ],
            rew: true
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'name', 'email']
          },
          {
            model: User,
            as: 'publisher',
            attributes: ['id', 'name', 'email'],
            required: false
          }
        ]
      });

      if (!announcement) {
        throw new Error('Announcement not found');
      }

      return announcement;
    } catch (error) {
      logger.error('Error getting announcement by ID:', error);
      throw error;
    }
  }

  /**
   * Update announcement
   */
  async updateAnnouncement(announcementId, updateData, userId) {
    try {
      const announcement = await Announcement.findByPk(announcementId);
      
      if (!announcement) {
        throw new Error('Announcement not found');
      }

      // Check permissions
      if (announcement.created_by !== userId) {
        throw new Error('Permission denied: Only creator can update announcement');
      }

      await announcement.update(updateData);
      
      logger.info(`Announcement updated: ${announcement.title} by user ${userId}`);
      
      return announcement;
    } catch (error) {
      logger.error('Error updating announcement:', error);
      throw error;
    }
  }

  /**
   * Publish announcement
   */
  async publishAnnouncement(announcementId, publisherId) {
    try {
      const announcement = await Announcement.findByPk(announcementId);
      
      if (!announcement) {
        throw new Error('Announcement not found');
      }

      await announcement.update({
        status: 'published',
        published_by: publisherId,
        published_at: new Date()
      });
      
      logger.info(`Announcement published: ${announcement.title} by user ${publisherId}`);
      
      return announcement;
    } catch (error) {
      logger.error('Error publishing announcement:', error);
      throw error;
    }
  }

  /**
   * Archive announcement
   */
  async archiveAnnouncement(announcementId, userId) {
    try {
      const announcement = await Announcement.findByPk(announcementId);
      
      if (!announcement) {
        throw new Error('Announcement not found');
      }

      // Check permissions
      if (announcement.created_by !== userId) {
        throw new Error('Permission denied: Only creator can archive announcement');
      }

      await announcement.update({ status: 'archived' });
      
      logger.info(`Announcement archived: ${announcement.title} by user ${userId}`);
      
      return announcement;
    } catch (error) {
      logger.error('Error archiving announcement:', error);
      throw error;
    }
  }

  /**
   * Toggle pin status
   */
  async togglePinStatus(announcementId, userId) {
    try {
      const announcement = await Announcement.findByPk(announcementId);
      
      if (!announcement) {
        throw new Error('Announcement not found');
      }

      // Check permissions
      if (announcement.created_by !== userId) {
        throw new Error('Permission denied: Only creator can pin/unpin announcement');
      }

      await announcement.update({ is_pinned: !announcement.is_pinned });
      
      logger.info(`Announcement pin status toggled: ${announcement.title} by user ${userId}`);
      
      return announcement;
    } catch (error) {
      logger.error('Error toggling pin status:', error);
      throw error;
    }
  }

  /**
   * Delete announcement
   */
  async deleteAnnouncement(announcementId, userId) {
    try {
      const announcement = await Announcement.findByPk(announcementId);
      
      if (!announcement) {
        throw new Error('Announcement not found');
      }

      // Check permissions
      if (announcement.created_by !== userId) {
        throw new Error('Permission denied: Only creator can delete announcement');
      }

      await announcement.destroy();
      
      logger.info(`Announcement deleted: ${announcement.title} by user ${userId}`);
      
      return { success: true, message: 'Announcement deleted successfully' };
    } catch (error) {
      logger.error('Error deleting announcement:', error);
      throw error;
    }
  }

  /**
   * Get announcements for instructor dashboard
   */
  async getInstructorAnnouncements(instructorId, courseId = null) {
    try {
      const whereClause = {};
      
      if (courseId) {
        whereClause.course_id = courseId;
      } else {
        // Get all courses where user is instructor
        const courses = await LtiContext.findAll({
          where: { instructor_id: instructorId },
          attributes: ['id']
        });
        
        if (courses.length === 0) {
          return { announcements: [], pagination: { page: 1, limit: 20, total: 0, pages: 0 } };
        }
        
        whereClause.course_id = { [Op.in]: courses.map(c => c.id) };
      }

      const { count, rows: announcements } = await Announcement.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: LtiContext,
            as: 'course',
            attributes: [
              'id',
              ['context_title', 'name'],
              ['context_label', 'code'],
            ],
            raw: true
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'name', 'email']
          }
        ],
        order: [
          ['is_pinned', 'DESC'],
          ['priority', 'DESC'],
          ['created_at', 'DESC']
        ],
        limit: 50
      });

      return {
        announcements,
        totalCount: count
      };
    } catch (error) {
      logger.error('Error getting instructor announcements:', error);
      throw error;
    }
  }

  /**
   * Get announcements for student view
   */
  async getStudentAnnouncements(studentId, courseId = null) {
    try {
      const whereClause = {
        status: 'published'
      };
      
      if (courseId) {
        whereClause.course_id = courseId;
      }

      // Handle expired announcements
      whereClause[Op.or] = [
        { expires_at: null },
        { expires_at: { [Op.gt]: new Date() } }
      ];

      const announcements = await Announcement.findAll({
        where: whereClause,
        include: [
          {
            model: LtiContext,
            as: 'course',
            attributes: [
              'id',
              ['context_title', 'name'],
              ['context_label', 'code'],
            ],
            raw: true
          },
          {
            model: User,
            as: 'creator',
            attributes: ['id', 'name', 'email']
          }
        ],
        order: [
          ['is_pinned', 'DESC'],
          ['priority', 'DESC'],
          ['created_at', 'DESC']
        ],
        limit: 50
      });

      return announcements;
    } catch (error) {
      logger.error('Error getting student announcements:', error);
      throw error;
    }
  }

  /**
   * Schedule announcement for future publication
   */
  async scheduleAnnouncement(announcementId, scheduledFor, userId) {
    try {
      const announcement = await Announcement.findByPk(announcementId);
      
      if (!announcement) {
        throw new Error('Announcement not found');
      }

      // Check permissions
      if (announcement.created_by !== userId) {
        throw new Error('Permission denied: Only creator can schedule announcement');
      }

      await announcement.update({
        scheduled_for: scheduledFor,
        status: 'draft'
      });
      
      logger.info(`Announcement scheduled: ${announcement.title} for ${scheduledFor} by user ${userId}`);
      
      return announcement;
    } catch (error) {
      logger.error('Error scheduling announcement:', error);
      throw error;
    }
  }

  /**
   * Process scheduled announcements (to be called by cron job)
   */
  async processScheduledAnnouncements() {
    try {
      const now = new Date();
      
      const scheduledAnnouncements = await Announcement.findAll({
        where: {
          status: 'draft',
          scheduled_for: { [Op.lte]: now }
        }
      });

      const publishedCount = 0;
      
      for (const announcement of scheduledAnnouncements) {
        await announcement.update({
          status: 'published',
          published_at: now
        });
        publishedCount++;
      }

      if (publishedCount > 0) {
        logger.info(`Processed ${publishedCount} scheduled announcements`);
      }

      return publishedCount;
    } catch (error) {
      logger.error('Error processing scheduled announcements:', error);
      throw error;
    }
  }
}

export default new AnnouncementService();
