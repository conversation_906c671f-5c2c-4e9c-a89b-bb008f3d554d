'use strict';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('lti_context_enrollments', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: { model: 'users', key: 'id' },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      context_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: { model: 'lti_contexts', key: 'id' },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      role_in_course: {
        type: Sequelize.ENUM('student', 'instructor', 'ta', 'observer'),
        allowNull: false,
        defaultValue: 'student'
      },
      enrollment_status: {
        type: Sequelize.ENUM('active', 'dropped', 'completed', 'withdrawn'),
        defaultValue: 'active'
      },
      enrolled_at: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      },
      dropped_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      final_grade: {
        type: Sequelize.DECIMAL(5, 2),
        allowNull: true,
        comment: 'Final context grade (0-100)'
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {},
        comment: 'Additional enrollment metadata from LMS (NRPS)'
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    await queryInterface.addConstraint('lti_context_enrollments', {
      fields: ['user_id', 'context_id'],
      type: 'unique',
      name: 'uq_lti_context_enrollments_user_context'
    });
    await queryInterface.addIndex('lti_context_enrollments', ['user_id']);
    await queryInterface.addIndex('lti_context_enrollments', ['context_id']);
    await queryInterface.addIndex('lti_context_enrollments', [
      'role_in_course'
    ]);
    await queryInterface.addIndex('lti_context_enrollments', [
      'enrollment_status'
    ]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('lti_context_enrollments');
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_lti_context_enrollments_role_in_course";'
    );
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_lti_context_enrollments_enrollment_status";'
    );
  }
};
