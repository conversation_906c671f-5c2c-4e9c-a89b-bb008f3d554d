'use strict';

export default {
  async up(queryInterface, Sequelize) {
    // Create activities table
    await queryInterface.createTable('activities', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      user_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      project_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'projects',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      course_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'courses',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      activity_type: {
        type: Sequelize.ENUM(
          'project_created',
          'project_published',
          'project_archived',
          'checkpoint_created',
          'checkpoint_published',
          'checkpoint_submitted',
          'checkpoint_graded',
          'grade_assigned',
          'feedback_given',
          'student_enrolled',
          'student_submitted',
          'student_graded',
          'course_created',
          'course_updated'
        ),
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {}
      },
      ip_address: {
        type: Sequelize.STRING,
        allowNull: true
      },
      user_agent: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Create project_statistics table
    await queryInterface.createTable('project_statistics', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      project_id: {
        type: Sequelize.UUID,
        allowNull: false,
        unique: true,
        references: {
          model: 'projects',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      total_students: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      active_students: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      submissions_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      graded_count: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      pending_grades: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      average_grade: {
        type: Sequelize.DECIMAL(5, 2),
        allowNull: true
      },
      progress_percentage: {
        type: Sequelize.DECIMAL(5, 2),
        allowNull: false,
        defaultValue: 0
      },
      checkpoint_completion_rate: {
        type: Sequelize.DECIMAL(5, 2),
        allowNull: false,
        defaultValue: 0
      },
      average_time_to_completion: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      last_activity: {
        type: Sequelize.DATE,
        allowNull: true
      },
      last_updated: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {}
      }
    });

    // Add start_date column to projects table
    await queryInterface.addColumn('projects', 'start_date', {
      type: Sequelize.DATE,
      allowNull: true,
      comment: 'When the project becomes available to students'
    });

    // Set start_date to created_at for existing projects
    await queryInterface.sequelize.query(`
      UPDATE projects 
      SET start_date = created_at 
      WHERE start_date IS NULL
    `);

    // Create indexes for activities table
    await queryInterface.addIndex('activities', ['user_id']);
    await queryInterface.addIndex('activities', ['project_id']);
    await queryInterface.addIndex('activities', ['course_id']);
    await queryInterface.addIndex('activities', ['activity_type']);
    await queryInterface.addIndex('activities', ['created_at']);
    await queryInterface.addIndex('activities', ['user_id', 'created_at']);
    await queryInterface.addIndex('activities', ['course_id', 'created_at']);

    // Create indexes for project_statistics table
    await queryInterface.addIndex('project_statistics', ['project_id']);
    await queryInterface.addIndex('project_statistics', ['last_updated']);
    await queryInterface.addIndex('project_statistics', [
      'progress_percentage'
    ]);
    await queryInterface.addIndex('project_statistics', ['average_grade']);

    // Create index for projects start_date
    await queryInterface.addIndex('projects', ['start_date']);
  },

  async down(queryInterface, Sequelize) {
    // Remove start_date column from projects table
    await queryInterface.removeColumn('projects', 'start_date');

    // Drop project_statistics table
    await queryInterface.dropTable('project_statistics');

    // Drop activities table
    await queryInterface.dropTable('activities');
  }
};
