'use strict';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    // Remove courseId column from lti_contexts table
    await queryInterface.removeColumn('lti_contexts', 'course_id');

    // Remove the index on course_id if it exists
    try {
      await queryInterface.removeIndex('lti_contexts', 'course_id');
    } catch (error) {
      // Index might not exist, ignore error
    }
  },

  async down(queryInterface, Sequelize) {
    // Add courseId column back to lti_contexts table
    await queryInterface.addColumn('lti_contexts', 'course_id', {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'courses',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });

    // Recreate the index on course_id
    await queryInterface.addIndex('lti_contexts', ['course_id']);
  }
};