'use strict';

export default {
  async up(queryInterface, Sequelize) {
    // Add new fields to projects table
    await queryInterface.addColumn('projects', 'project_type', {
      type: Sequelize.ENUM(
        'individual',
        'group',
        'research',
        'competition',
        'tutorial'
      ),
      allowNull: true,
      defaultValue: 'individual',
      comment: 'Type of project (individual, group, research, etc.)'
    });

    await queryInterface.addColumn('projects', 'total_points', {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: 100,
      comment: 'Total points available for the project'
    });

    await queryInterface.addColumn('projects', 'project_overview', {
      type: Sequelize.TEXT,
      allowNull: true,
      comment: 'Detailed project overview and context'
    });

    await queryInterface.addColumn('projects', 'requirements', {
      type: Sequelize.JSONB,
      allowNull: true,
      defaultValue: [],
      comment: 'Array of project requirements and deliverables'
    });

    await queryInterface.addColumn('projects', 'is_template', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Whether this project is a template'
    });

    await queryInterface.addColumn('projects', 'template_category', {
      type: Sequelize.STRING,
      allowNull: true,
      comment: 'Category for template organization'
    });

    await queryInterface.addColumn('projects', 'template_rating', {
      type: Sequelize.DECIMAL(3, 2),
      allowNull: true,
      comment: 'Template rating (0.00 to 5.00)'
    });

    await queryInterface.addColumn('projects', 'template_usage_count', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Number of times this template has been used'
    });

    await queryInterface.addColumn('projects', 'published_at', {
      type: Sequelize.DATE,
      allowNull: true,
      comment: 'When the project was published'
    });

    await queryInterface.addColumn('projects', 'published_by', {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
      comment: 'User who published the project'
    });

    await queryInterface.addColumn('projects', 'version', {
      type: Sequelize.STRING,
      allowNull: true,
      defaultValue: '1.0.0',
      comment: 'Project version for tracking changes'
    });

    await queryInterface.addColumn('projects', 'changelog', {
      type: Sequelize.JSONB,
      allowNull: true,
      defaultValue: [],
      comment: 'Array of version changes and updates'
    });

    // Create indexes for new fields
    await queryInterface.addIndex('projects', ['project_type']);
    await queryInterface.addIndex('projects', ['is_template']);
    await queryInterface.addIndex('projects', ['template_category']);
    await queryInterface.addIndex('projects', ['template_rating']);
    await queryInterface.addIndex('projects', ['published_at']);
    await queryInterface.addIndex('projects', ['published_by']);

    // Add comments to new columns
    await queryInterface.sequelize.query(`
      COMMENT ON COLUMN projects.project_type IS 'Type of project (individual, group, research, etc.)';
      COMMENT ON COLUMN projects.total_points IS 'Total points available for the project';
      COMMENT ON COLUMN projects.project_overview IS 'Detailed project overview and context';
      COMMENT ON COLUMN projects.requirements IS 'Array of project requirements and deliverables';
      COMMENT ON COLUMN projects.is_template IS 'Whether this project is a template';
      COMMENT ON COLUMN projects.template_category IS 'Category for template organization';
      COMMENT ON COLUMN projects.template_rating IS 'Template rating (0.00 to 5.00)';
      COMMENT ON COLUMN projects.template_usage_count IS 'Number of times this template has been used';
      COMMENT ON COLUMN projects.published_at IS 'When the project was published';
      COMMENT ON COLUMN projects.published_by IS 'User who published the project';
      COMMENT ON COLUMN projects.version IS 'Project version for tracking changes';
      COMMENT ON COLUMN projects.changelog IS 'Array of version changes and updates';
    `);
  },

  async down(queryInterface, Sequelize) {
    // Remove indexes
    await queryInterface.removeIndex('projects', ['project_type']);
    await queryInterface.removeIndex('projects', ['is_template']);
    await queryInterface.removeIndex('projects', ['template_category']);
    await queryInterface.removeIndex('projects', ['template_rating']);
    await queryInterface.removeIndex('projects', ['published_at']);
    await queryInterface.removeIndex('projects', ['published_by']);

    // Remove columns
    await queryInterface.removeColumn('projects', 'changelog');
    await queryInterface.removeColumn('projects', 'version');
    await queryInterface.removeColumn('projects', 'published_by');
    await queryInterface.removeColumn('projects', 'published_at');
    await queryInterface.removeColumn('projects', 'template_usage_count');
    await queryInterface.removeColumn('projects', 'template_rating');
    await queryInterface.removeColumn('projects', 'template_category');
    await queryInterface.removeColumn('projects', 'is_template');
    await queryInterface.removeColumn('projects', 'requirements');
    await queryInterface.removeColumn('projects', 'project_overview');
    await queryInterface.removeColumn('projects', 'total_points');
    await queryInterface.removeColumn('projects', 'project_type');

    // Remove ENUM type
    await queryInterface.sequelize.query(`
      DROP TYPE IF EXISTS "enum_projects_project_type";
    `);
  }
};
