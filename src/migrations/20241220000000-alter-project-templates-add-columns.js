'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add total_points column
    await queryInterface.addColumn('project_templates', 'total_points', {
      type: Sequelize.INTEGER,
      allowNull: true,
      comment: 'total points for the project'
    });

    // Add learning_objectives column
    await queryInterface.addColumn('project_templates', 'learning_objectives', {
      type: Sequelize.TEXT,
      allowNull: true,
      comment: 'learning objectives for the project'
    });

    // Add prerequisites column
    await queryInterface.addColumn('project_templates', 'prerequisites', {
      type: Sequelize.TEXT,
      allowNull: true,
      comment: 'Prerequisite topics or projects'
    });

    // Add deleted_by column
    await queryInterface.addColumn('project_templates', 'deleted_by', {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });

    // Add is_deleted column with default false
    await queryInterface.addColumn('project_templates', 'is_deleted', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'Flag to indicate if the project is deleted'
    });

    // Backfill existing records with is_deleted = false
    await queryInterface.sequelize.query(`
      UPDATE project_templates 
      SET is_deleted = false 
      WHERE is_deleted IS NULL;
    `);
  },

  async down(queryInterface, Sequelize) {
    // Remove columns in reverse order
    await queryInterface.removeColumn('project_templates', 'is_deleted');
    await queryInterface.removeColumn('project_templates', 'deleted_by');
    await queryInterface.removeColumn('project_templates', 'prerequisites');
    await queryInterface.removeColumn('project_templates', 'learning_objectives');
    await queryInterface.removeColumn('project_templates', 'total_points');
  }
};