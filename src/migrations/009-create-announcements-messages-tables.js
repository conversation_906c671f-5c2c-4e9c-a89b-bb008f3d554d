'use strict';

export default {
  async up(queryInterface, Sequelize) {
    // Create announcements table
    await queryInterface.createTable('announcements', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      course_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'courses',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      title: {
        type: Sequelize.STRING(200),
        allowNull: false
      },
      content: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      announcement_type: {
        type: Sequelize.ENUM(
          'general',
          'project_update',
          'deadline_reminder',
          'course_update',
          'important',
          'urgent'
        ),
        allowNull: false,
        defaultValue: 'general'
      },
      priority: {
        type: Sequelize.ENUM('low', 'normal', 'high', 'urgent'),
        allowNull: false,
        defaultValue: 'normal'
      },
      status: {
        type: Sequelize.ENUM('draft', 'published', 'archived'),
        allowNull: false,
        defaultValue: 'draft'
      },
      is_pinned: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      scheduled_for: {
        type: Sequelize.DATE,
        allowNull: true
      },
      expires_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      target_audience: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: ['all']
      },
      attachments: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: []
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {}
      },
      created_by: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      published_by: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      published_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Create messages table
    await queryInterface.createTable('messages', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true
      },
      sender_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      recipient_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      subject: {
        type: Sequelize.STRING(200),
        allowNull: false
      },
      content: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      message_type: {
        type: Sequelize.ENUM(
          'personal',
          'course_related',
          'project_related',
          'system',
          'notification'
        ),
        allowNull: false,
        defaultValue: 'personal'
      },
      priority: {
        type: Sequelize.ENUM('low', 'normal', 'high', 'urgent'),
        allowNull: false,
        defaultValue: 'normal'
      },
      status: {
        type: Sequelize.ENUM('sent', 'delivered', 'read', 'archived'),
        allowNull: false,
        defaultValue: 'sent'
      },
      is_read: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      read_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      parent_message_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'messages',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      thread_id: {
        type: Sequelize.UUID,
        allowNull: true
      },
      course_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'courses',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      project_id: {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: 'projects',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      attachments: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: []
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true,
        defaultValue: {}
      },
      scheduled_for: {
        type: Sequelize.DATE,
        allowNull: true
      },
      expires_at: {
        type: Sequelize.DATE,
        allowNull: true
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW
      }
    });

    // Create indexes for announcements table
    await queryInterface.addIndex('announcements', ['course_id']);
    await queryInterface.addIndex('announcements', ['created_by']);
    await queryInterface.addIndex('announcements', ['status']);
    await queryInterface.addIndex('announcements', ['announcement_type']);
    await queryInterface.addIndex('announcements', ['priority']);
    await queryInterface.addIndex('announcements', ['is_pinned']);
    await queryInterface.addIndex('announcements', ['scheduled_for']);
    await queryInterface.addIndex('announcements', ['expires_at']);
    await queryInterface.addIndex('announcements', ['created_at']);
    await queryInterface.addIndex('announcements', ['course_id', 'status']);
    await queryInterface.addIndex('announcements', ['course_id', 'is_pinned']);

    // Create indexes for messages table
    await queryInterface.addIndex('messages', ['sender_id']);
    await queryInterface.addIndex('messages', ['recipient_id']);
    await queryInterface.addIndex('messages', ['status']);
    await queryInterface.addIndex('messages', ['is_read']);
    await queryInterface.addIndex('messages', ['message_type']);
    await queryInterface.addIndex('messages', ['priority']);
    await queryInterface.addIndex('messages', ['parent_message_id']);
    await queryInterface.addIndex('messages', ['thread_id']);
    await queryInterface.addIndex('messages', ['course_id']);
    await queryInterface.addIndex('messages', ['project_id']);
    await queryInterface.addIndex('messages', ['created_at']);
    await queryInterface.addIndex('messages', ['recipient_id', 'is_read']);
    await queryInterface.addIndex('messages', ['recipient_id', 'status']);
    await queryInterface.addIndex('messages', ['sender_id', 'recipient_id']);

    // Add comments to tables
    await queryInterface.sequelize.query(`
      COMMENT ON TABLE announcements IS 'Course announcements and notifications';
      COMMENT ON TABLE messages IS 'User-to-user messaging system';
      
      COMMENT ON COLUMN announcements.target_audience IS 'Array of target roles: all, students, instructors, tas';
      COMMENT ON COLUMN announcements.attachments IS 'Array of attachment metadata (S3 URLs, file names, etc.)';
      COMMENT ON COLUMN announcements.metadata IS 'Additional announcement metadata';
      COMMENT ON COLUMN announcements.scheduled_for IS 'When to automatically publish the announcement';
      COMMENT ON COLUMN announcements.expires_at IS 'When the announcement expires';
      
      COMMENT ON COLUMN messages.thread_id IS 'Group messages in the same conversation thread';
      COMMENT ON COLUMN messages.attachments IS 'Array of attachment metadata (S3 URLs, file names, etc.)';
      COMMENT ON COLUMN messages.metadata IS 'Additional message metadata';
      COMMENT ON COLUMN messages.scheduled_for IS 'When to send the message (for scheduled messages)';
      COMMENT ON COLUMN messages.expires_at IS 'When the message expires';
    `);
  },

  async down(queryInterface, Sequelize) {
    // Drop tables in reverse order
    await queryInterface.dropTable('messages');
    await queryInterface.dropTable('announcements');
  }
};
