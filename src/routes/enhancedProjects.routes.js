import express from 'express';
import {
  createEnhancedProject,
  createProjectTemplate,
  getProjectTemplates,
  getTemplateById,
  updateProjectTemplate,
  deleteProjectTemplate,
  featureProjectTemplate,
  assignUsersToProject,
  getProjectAssignments,
  removeUserAssignment,
  publishProject,
  unpublishProject,
  saveProjectAsDraft,
  getProjectWithDetails,
  duplicateProjectFromTemplate,
  rateProjectTemplate,
  getProjectsByUserAssignment,
  getUserProjectWorkload,
  getProjectAssignmentStats,
  getEnhancedProject,
  updateEnhancedProject,
  duplicateProject,
  deleteProject
} from '../controllers/enhancedProject.controller.js';
import { jwtMiddleware } from '../middlewares/auth.middlewares.js';
import { requirePermissions } from '../middlewares/rbac.middlewares.js';
import { body, query, param } from 'express-validator';
import { validate } from '../middlewares/validation.middlewares.js';

const router = express.Router();

// Apply JWT middleware to all routes
router.use(jwtMiddleware);

// Create/update enhanced project
router.post(
  '/enhanced',
  [
    requirePermissions(['create_projects']),
    body('title')
      .optional({ checkFalsy: false })
      .trim()
      .isLength({ min: 2, max: 200 })
      .withMessage('Title must be between 2 and 200 characters'),
    body('description').optional({ checkFalsy: false }).trim(),
    body('courseId')
      .optional({ checkFalsy: false })
      .if(body('courseId').notEmpty())
      .isUUID()
      .withMessage('Valid course ID is required'),
    body('projectType')
      .optional({ checkFalsy: false })
      .if(body('projectType').notEmpty())
      .isIn(['individual', 'group', 'research', 'competition', 'tutorial'])
      .withMessage('Invalid project type'),
    body('difficulty_level')
      .optional({ checkFalsy: false })
      .if(body('difficulty_level').notEmpty())
      .isIn(['beginner', 'intermediate', 'advanced'])
      .withMessage('Invalid difficulty level'),
    body('estimatedHours')
      .optional({ checkFalsy: false })
      .if(body('estimatedHours').notEmpty())
      .isInt({ min: 1 })
      .withMessage('Estimated hours must be a positive integer'),
    body('totalPoints')
      .optional({ checkFalsy: false })
      .if(body('totalPoints').notEmpty())
      .isInt({ min: 1 })
      .withMessage('Total points must be a positive integer'),
    body('dueDate')
      .optional({ checkFalsy: false })
      .if(body('dueDate').notEmpty())
      .isISO8601()
      .withMessage('Due date must be a valid ISO 8601 date'),
    body('startDate')
      .optional({ checkFalsy: false })
      .if(body('startDate').notEmpty())
      .isISO8601()
      .withMessage('Start date must be a valid ISO 8601 date'),
    body('learning_objectives')
      .optional({ checkFalsy: false })
      .isString()
      .trim()
      .withMessage('Learning objectives must be a string'),
    body('prerequisites')
      .optional({ checkFalsy: false })
      .isString()
      .trim()
      .withMessage('Prerequisites must be a string'),
    body('project_overview').optional({ checkFalsy: false }),
    body('instructions').optional({ checkFalsy: false }),
    body('skillsCovered')
      .optional({ checkFalsy: false })
      .isArray()
      .withMessage('Skills covered must be an array'),
    body('technologiesUsed')
      .optional({ checkFalsy: false })
      .isArray()
      .withMessage('Technologies used must be an array'),
    body('tags')
      .optional({ checkFalsy: false })
      .isArray()
      .withMessage('Tags must be an array'),
    body('isTemplate')
      .optional({ checkFalsy: false })
      .isBoolean()
      .withMessage('isTemplate must be a boolean'),
    body('templateCategory')
      .optional({ checkFalsy: false })
      .if(body('isTemplate').custom(v => v === true))
      .isString()
      .trim()
      .withMessage('Template category must be a string'),
    body('templateSubcategory')
      .optional({ checkFalsy: false })
      .if(body('isTemplate').custom(v => v === true))
      .isString()
      .trim()
      .withMessage('Template subcategory must be a string'),

    body('template_id')
      .optional({ checkFalsy: false })
      .if(body('template_id').notEmpty())
      .isUUID()
      .withMessage('Valid template ID is required'),
    body('assignments')
      .optional({ checkFalsy: false })
      .isArray()
      .withMessage('Assignments must be an array'),
    body('categoryId')
      .optional({ checkFalsy: false })
      .isUUID()
      .withMessage('Valid category ID is required'),
    body('instructorIds')
      .optional({ checkFalsy: false })
      .isArray()
      .withMessage('instructorIds must be an array of UUIDs'),
    body('instructorIds.*')
      .optional({ checkFalsy: false })
      .isUUID()
      .withMessage('Each instructorIds item must be a UUID'),
    body('teachingAssId')
      .optional({ checkFalsy: false })
      .isArray()
      .withMessage('teachingAssId must be an array of UUIDs'),
    body('teachingAssId.*')
      .optional({ checkFalsy: false })
      .isUUID()
      .withMessage('Each teachingAssId item must be a UUID'),
    body('maxSubmissions')
      .optional({ checkFalsy: false })
      .isInt({ min: 0 })
      .withMessage('maxSubmissions must be an integer'),
    body('lateSubmissionsAllowed')
      .optional({ checkFalsy: false })
      .isBoolean()
      .withMessage('lateSubmissionsAllowed must be boolean'),
    body('isScreen')
      .exists({ checkFalsy: true })
      .withMessage('isScreen is required')
      .isInt({ min: 1, max: 4 })
      .withMessage('isScreen must be a number between 1 and 4'),
    body('id')
      .if(body('isScreen').custom(v => Number(v) > 1)) // only run next checks when > 1
      .exists({ checkFalsy: true })
      .withMessage('id is required when isScreen > 1')
      .bail()
      .isUUID()
      .withMessage('id must be a valid UUID'),
    body('sandbox_time_duration')
      .optional({ checkFalsy: false })
      .if(body('sandbox_time_duration').notEmpty())
      .matches(/^(\d{1,3}):([0-5]\d)$/)
      .withMessage(
        'Sandbox duration must be in HHH:MM format (0:00 to 999:59)'
      ),
    body('late_submission_days_allowed')
      .optional({ checkFalsy: false })
      .if(body('late_submission_days_allowed').notEmpty())
      .isInt({ min: 0, max: 365 })
      .withMessage('Late submission days must be between 0 and 365')
  ],
  validate,
  createEnhancedProject
);

//Get all the project
router.get(
  '/enhanced',
  [
    requirePermissions(['view_projects']),
    query('page').optional().isInt({ min: 1 }).toInt(),
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
    query('search').optional().trim(),
    query('courseId').optional().isUUID(),
    query('status').optional().isIn(['draft', 'published', 'archived']),
    query('difficultyLevel')
      .optional()
      .isIn(['beginner', 'intermediate', 'advanced']),
    query('isLinked').optional().isBoolean().toBoolean()
  ],
  validate,
  getEnhancedProject
);

// Get project by id with full details
router.get(
  '/:id/details',
  [
    requirePermissions(['project:read']),
    param('id').isUUID().withMessage('Valid project ID is required')
  ],
  validate,
  getProjectWithDetails
);

// Publish project
router.put(
  '/:id/publish',
  [
    requirePermissions(['publish_projects']),
    param('id').isUUID().withMessage('Valid project ID is required')
  ],
  validate,
  publishProject
);

// Unpublish project
router.put(
  '/:id/unpublish',
  [
    requirePermissions(['publish_projects']),
    param('id').isUUID().withMessage('Valid project ID is required')
  ],
  validate,
  unpublishProject
);

// duplicate project
/**
 * @swagger
 * /api/projects/{id}/duplicate:
 *   post:
 *     summary: Duplicate project
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - courseId
 *             properties:
 *               courseId:
 *                 type: string
 *                 format: uuid
 *               title:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 200
 *     responses:
 *       201:
 *         description: Project duplicated successfully
 *       404:
 *         description: Project not found
 *       403:
 *         description: Insufficient permissions
 */
router.post(
  '/:id/duplicate',
  [
    requirePermissions(['create_projects']),
    param('id').isUUID(),
    body('courseId').optional().if(body('courseId').notEmpty()).isUUID(),
    body('title')
      .optional()
      .if(body('title').notEmpty())
      .isLength({ min: 2, max: 200 })
      .trim()
  ],
  validate,
  duplicateProject
);

// Delete project
/**
 * @swagger
 * /api/projects/{id}:
 *   delete:
 *     summary: Delete project
 *     tags: [Projects]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Project deleted successfully
 *       404:
 *         description: Project not found
 *       403:
 *         description: Insufficient permissions
 *       409:
 *         description: Cannot delete project with submissions
 */
router.delete(
  '/:id',
  [requirePermissions(['delete_projects']), param('id').isUUID()],
  validate,
  deleteProject
);

// Create project template
router.post(
  '/templates',
  [
    requirePermissions(['create_projects']),
    body('projectId').isUUID().withMessage('Valid project ID is required'),
    body('title')
      .trim()
      .isLength({ min: 2, max: 100 })
      .withMessage('Template name must be between 2 and 100 characters'),
    body('description')
      .optional({ checkFalsy: false })
      .if(body('description').notEmpty())
      .isString()
      .trim()
      .withMessage('Template description must be a string'),
    body('template_category')
      .optional({ checkFalsy: false })
      .if(body('template_category').notEmpty())
      .isString()
      .trim()
      .withMessage('Template category must be a string'),
    body('template_subcategory')
      .optional({ checkFalsy: false })
      .if(body('template_subcategory').notEmpty())
      .isString()
      .trim()
      .withMessage('Template subcategory must be a string'),
    body('difficulty_level')
      .optional({ checkFalsy: false })
      .if(body('difficulty_level').notEmpty())
      .isIn(['beginner', 'intermediate', 'advanced'])
      .withMessage('Invalid difficulty level'),
    body('total_points')
      .optional({ checkFalsy: false })
      .if(body('total_points').notEmpty())
      .isInt({ min: 1 })
      .withMessage('Total points must be a positive integer'),
    body('estimated_hours')
      .optional({ checkFalsy: false })
      .if(body('estimated_hours').notEmpty())
      .isInt({ min: 1 })
      .withMessage('Estimated hours must be a positive integer'),
    body('learning_objectives')
      .optional({ checkFalsy: false })
      .if(body('learning_objectives').notEmpty())
      .isString()
      .trim()
      .withMessage('Learning objectives must be a string'),
    body('prerequisites')
      .optional({ checkFalsy: false })
      .if(body('prerequisites').notEmpty())
      .isString()
      .trim()
      .withMessage('Prerequisites must be a string'),
    body('skills_covered')
      .optional({ checkFalsy: false })
      .if(body('skills_covered').notEmpty())
      .isArray()
      .withMessage('Skills covered must be an array'),
    body('technologies_used')
      .optional({ checkFalsy: false })
      .if(body('technologies_used').notEmpty())
      .isArray()
      .withMessage('Technologies used must be an array'),
    body('tags')
      .optional({ checkFalsy: false })
      .if(body('tags').notEmpty())
      .isArray()
      .withMessage('Tags must be an array')
  ],
  validate,
  createProjectTemplate
);

// Get project templates
router.get(
  '/templates',
  [
    requirePermissions(['view_projects']),
    query('category').optional().trim().notEmpty(),
    query('subcategory').optional().trim().notEmpty(),
    query('difficultyLevel')
      .optional()
      .isIn(['beginner', 'intermediate', 'advanced'])
      .withMessage('Invalid difficulty level'),
    query('isFeatured').optional().isBoolean().toBoolean(),
    query('isPublic').optional().isBoolean().toBoolean(),
    query('page').optional().isInt({ min: 1 }).toInt(),
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
    query('search').optional().trim().notEmpty()
  ],
  validate,
  getProjectTemplates
);

// Assign users to project
router.post(
  '/:id/assignments',
  [
    requirePermissions(['project:assign_users']),
    param('id').isUUID().withMessage('Valid project ID is required'),
    body('assignments')
      .isArray({ min: 1 })
      .withMessage(
        'Assignments array is required with at least one assignment'
      ),
    body('assignments.*.userId')
      .isUUID()
      .withMessage('Each assignment must have a valid user ID'),
    body('assignments.*.role')
      .isIn(['instructor', 'ta', 'reviewer', 'mentor'])
      .withMessage('Each assignment must have a valid role'),
    body('assignments.*.assignmentType')
      .optional()
      .isIn(['primary', 'secondary', 'guest'])
      .withMessage('Invalid assignment type'),
    body('assignments.*.startDate')
      .optional()
      .isISO8601()
      .withMessage('Start date must be a valid ISO 8601 date'),
    body('assignments.*.endDate')
      .optional()
      .isISO8601()
      .withMessage('End date must be a valid ISO 8601 date')
  ],
  validate,
  assignUsersToProject
);

// Get project assignments
router.get(
  '/:id/assignments',
  [
    requirePermissions(['project:view_assignments']),
    param('id').isUUID().withMessage('Valid project ID is required')
  ],
  validate,
  getProjectAssignments
);

// Remove user assignment from project
router.delete(
  '/:id/assignments/:userId',
  [
    requirePermissions(['project:manage_assignments']),
    param('id').isUUID().withMessage('Valid project ID is required'),
    param('userId').isUUID().withMessage('Valid user ID is required')
  ],
  validate,
  removeUserAssignment
);

// Save project as draft
router.post(
  '/:id/save-draft',
  [
    requirePermissions(['project:update']),
    param('id').isUUID().withMessage('Valid project ID is required')
  ],
  validate,
  saveProjectAsDraft
);

// Duplicate project from template
router.post(
  '/templates/:id/duplicate',
  [requirePermissions(['project:create'])],
  validate,
  duplicateProjectFromTemplate
);

// Rate project template
router.post(
  '/templates/:id/rate',
  [
    requirePermissions(['project_template:rate']),
    param('id').isUUID().withMessage('Valid template ID is required'),
    body('rating')
      .isFloat({ min: 0, max: 5 })
      .withMessage('Rating must be between 0 and 5')
  ],
  validate,
  rateProjectTemplate
);

// Get template by ID
router.get(
  '/templates/:id',
  [
    requirePermissions(['view_projects']),
    param('id').isUUID().withMessage('Valid template ID is required')
  ],
  validate,
  getTemplateById
);

// Update project template
router.put(
  '/templates/:id',
  [
    requirePermissions(['edit_projects']),
    param('id').isUUID().withMessage('Valid template ID is required'),
    body('projectId').isUUID().withMessage('Valid project ID is required'),
    body('title')
      .trim()
      .isLength({ min: 2, max: 100 })
      .withMessage('Template name must be between 2 and 100 characters'),
    body('description')
      .optional({ checkFalsy: false })
      .if(body('description').notEmpty())
      .isString()
      .trim()
      .withMessage('Template description must be a string'),
    body('template_category')
      .optional({ checkFalsy: false })
      .if(body('template_category').notEmpty())
      .isString()
      .trim()
      .withMessage('Template category must be a string'),
    body('template_subcategory')
      .optional({ checkFalsy: false })
      .if(body('template_subcategory').notEmpty())
      .isString()
      .trim()
      .withMessage('Template subcategory must be a string'),
    body('difficulty_level')
      .optional({ checkFalsy: false })
      .if(body('difficulty_level').notEmpty())
      .isIn(['beginner', 'intermediate', 'advanced'])
      .withMessage('Invalid difficulty level'),
    body('total_points')
      .optional({ checkFalsy: false })
      .if(body('total_points').notEmpty())
      .isInt({ min: 1 })
      .withMessage('Total points must be a positive integer'),
    body('estimated_hours')
      .optional({ checkFalsy: false })
      .if(body('estimated_hours').notEmpty())
      .isInt({ min: 1 })
      .withMessage('Estimated hours must be a positive integer'),
    body('learning_objectives')
      .optional({ checkFalsy: false })
      .if(body('learning_objectives').notEmpty())
      .isString()
      .trim()
      .withMessage('Learning objectives must be a string'),
    body('prerequisites')
      .optional({ checkFalsy: false })
      .if(body('prerequisites').notEmpty())
      .isString()
      .trim()
      .withMessage('Prerequisites must be a string'),
    body('skills_covered')
      .optional({ checkFalsy: false })
      .if(body('skills_covered').notEmpty())
      .isArray()
      .withMessage('Skills covered must be an array'),
    body('technologies_used')
      .optional({ checkFalsy: false })
      .if(body('technologies_used').notEmpty())
      .isArray()
      .withMessage('Technologies used must be an array'),
    body('tags')
      .optional({ checkFalsy: false })
      .if(body('tags').notEmpty())
      .isArray()
      .withMessage('Tags must be an array'),
    body('is_public')
      .optional({ checkFalsy: false })
      .if(body('is_public').notEmpty())
      .isBoolean()
      .withMessage('is_public must be a boolean')
    /* body('is_featured')
      .optional({ checkFalsy: false })
      .if(body('is_featured').notEmpty())
      .isBoolean()
      .withMessage('is_featured must be a boolean') */
  ],
  validate,
  updateProjectTemplate
);

// Delete project template
router.delete(
  '/templates/:id',
  [
    requirePermissions(['delete_projects']),
    param('id').isUUID().withMessage('Valid template ID is required')
  ],
  validate,
  deleteProjectTemplate
);

// Feature/Unfeature project template
router.put(
  '/templates/:id/feature',
  [
    requirePermissions(['admin']),
    param('id').isUUID().withMessage('Valid template ID is required'),
    body('featured').isBoolean().withMessage('Featured must be a boolean value')
  ],
  validate,
  featureProjectTemplate
);

// Assignment integration routes
router.get(
  '/assignments/user/:role?',
  [
    requirePermissions(['project:read']),
    param('role')
      .optional()
      .isIn(['instructor', 'ta', 'reviewer', 'mentor'])
      .withMessage('Invalid role. Must be instructor, ta, reviewer, or mentor')
  ],
  validate,
  getProjectsByUserAssignment
);

//getUserProjectWorkload
router.get(
  '/workload',
  [requirePermissions(['project:read'])],
  getUserProjectWorkload
);

//getProjectAssignmentStats
router.get(
  '/:id/assignment-stats',
  [
    requirePermissions(['project:view_assignments']),
    param('id').isUUID().withMessage('Valid project ID is required')
  ],
  validate,
  getProjectAssignmentStats
);

// Update enhanced project
router.put(
  '/:id/enhanced',
  [
    requirePermissions(['edit_projects']),
    param('id').isUUID().withMessage('Valid project ID is required'),
    body('title')
      .optional({ checkFalsy: false })
      .trim()
      .isLength({ min: 2, max: 200 })
      .withMessage('Title must be between 2 and 200 characters'),
    body('description').optional({ checkFalsy: false }).trim(),
    body('courseId')
      .optional({ checkFalsy: false })
      .if(body('courseId').notEmpty())
      .isUUID()
      .withMessage('Valid course ID is required'),
    body('projectType')
      .optional({ checkFalsy: false })
      .if(body('projectType').notEmpty())
      .isIn(['individual', 'group', 'research', 'competition', 'tutorial'])
      .withMessage('Invalid project type'),
    body('difficulty_level')
      .optional({ checkFalsy: false })
      .if(body('difficulty_level').notEmpty())
      .isIn(['beginner', 'intermediate', 'advanced'])
      .withMessage('Invalid difficulty level'),
    body('estimatedHours')
      .optional({ checkFalsy: false })
      .if(body('estimatedHours').notEmpty())
      .isInt({ min: 1 })
      .withMessage('Estimated hours must be a positive integer'),
    body('totalPoints')
      .optional({ checkFalsy: false })
      .if(body('totalPoints').notEmpty())
      .isInt({ min: 1 })
      .withMessage('Total points must be a positive integer'),
    body('dueDate')
      .optional({ checkFalsy: false })
      .if(body('dueDate').notEmpty())
      .isISO8601()
      .withMessage('Due date must be a valid ISO 8601 date'),
    body('startDate')
      .optional({ checkFalsy: false })
      .if(body('startDate').notEmpty())
      .isISO8601()
      .withMessage('Start date must be a valid ISO 8601 date'),
    body('learning_objectives').optional({ checkFalsy: false }),
    body('instructions').optional({ checkFalsy: false }),
    body('prerequisites').optional({ checkFalsy: false }),
    body('project_overview').optional({ checkFalsy: false }),
    body('skillsCovered')
      .optional({ checkFalsy: false })
      .if(body('skillsCovered').notEmpty())
      .isArray()
      .withMessage('Skills covered must be an array'),
    body('technologiesUsed')
      .optional({ checkFalsy: false })
      .if(body('technologiesUsed').notEmpty())
      .isArray()
      .withMessage('Technologies used must be an array'),
    body('tags')
      .optional({ checkFalsy: false })
      .if(body('tags').notEmpty())
      .isArray()
      .withMessage('Tags must be an array'),
    body('isTemplate')
      .optional({ checkFalsy: false })
      .if(body('isTemplate').notEmpty())
      .isBoolean()
      .withMessage('isTemplate must be a boolean'),
    body('assignments')
      .optional({ checkFalsy: false })
      .if(body('assignments').notEmpty())
      .isArray()
      .withMessage('Assignments must be an array'),
    body('categoryId')
      .optional({ checkFalsy: false })
      .if(body('categoryId').notEmpty())
      .isUUID()
      .withMessage('Valid category ID is required'),
    body('instructorIds')
      .optional({ checkFalsy: false })
      .if(body('instructorIds').notEmpty())
      .isArray()
      .withMessage('instructorIds must be an array of UUIDs'),
    body('instructorIds.*')
      .optional({ checkFalsy: false })
      .if(body('instructorIds.*').notEmpty())
      .isUUID()
      .withMessage('Each instructorIds item must be a UUID'),
    body('teachingAssId')
      .optional({ checkFalsy: false })
      .if(body('teachingAssId').notEmpty())
      .isArray()
      .withMessage('teachingAssId must be an array of UUIDs'),
    body('teachingAssId.*')
      .optional({ checkFalsy: false })
      .isUUID()
      .withMessage('Each instructorIds item must be a UUID'),
    body('teachingAssId')
      .optional({ checkFalsy: false })
      .isArray()
      .withMessage('teachingAssId must be an array of UUIDs'),
    body('teachingAssId.*')
      .optional({ checkFalsy: false })
      .isUUID()
      .withMessage('Each teachingAssId item must be a UUID'),
    body('maxSubmissions')
      .optional({ checkFalsy: false })
      .isInt({ min: 0 })
      .withMessage('maxSubmissions must be an integer'),
    body('lateSubmissionsAllowed')
      .optional({ checkFalsy: false })
      .isBoolean()
      .withMessage('lateSubmissionsAllowed must be boolean'),
    body('sandbox_time_duration')
      .optional({ checkFalsy: false })
      .if(body('sandbox_time_duration').notEmpty())
      .matches(/^(\d{1,3}):([0-5]\d)$/)
      .withMessage(
        'Sandbox duration must be in HHH:MM format (0:00 to 999:59)'
      ),
    body('late_submission_days_allowed')
      .optional({ checkFalsy: false })
      .if(body('late_submission_days_allowed').notEmpty())
      .isInt({ min: 0, max: 365 })
      .withMessage('Late submission days must be between 0 and 365')
  ],
  validate,
  updateEnhancedProject
);

export default router;
