import express from 'express';
import {
  getGrades,
  getGradeById,
  createOrUpdateGrade,
  updateGrade,
  deleteGrade,
  getGradingQueue,
  getGradeStatistics,
  bulkGradeSubmissions,
  createRubricBasedGrade,
  getSubmissionRubricData,
  getSubmissionGradingHistory,
  getSubmissionProgressDetails,
  getComprehensiveGradeStatistics,
  getStudentDetails
} from '../controllers/grade.controller.js';
import { requirePermissions } from '../middlewares/rbac.middlewares.js';
import { body, param, query } from 'express-validator';
import { validate } from '../middlewares/validation.middlewares.js';

const router = express.Router();

// Basic grade routes
router.get('/', [
  requirePermissions(['view_grades']),
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
  query('projectId').optional().isUUID(),
  query('courseId').optional().isUUID(),
  query('studentId').optional().isUUID(),
  query('evaluatorId').optional().isUUID()
], validate, getGrades);

router.get('/:id', [
  param('id').isUUID()
], validate, getGradeById);

router.post('/', [
  requirePermissions(['grade_submissions']),
  body('submissionId').isUUID(),
  body('totalScore').isFloat({ min: 0 }),
  body('maxScore').optional().isFloat({ min: 0 }),
  body('percentage').optional().isFloat({ min: 0, max: 100 }),
  body('letterGrade').optional().isString(),
  body('feedback').optional().isString(),
  body('rubricScores').optional().isObject()
], validate, createOrUpdateGrade);

router.put('/:id', [
  requirePermissions(['grade_submissions']),
  param('id').isUUID(),
  body('totalScore').optional().isFloat({ min: 0 }),
  body('maxScore').optional().isFloat({ min: 0 }),
  body('percentage').optional().isFloat({ min: 0, max: 100 }),
  body('letterGrade').optional().isString(),
  body('feedback').optional().isString(),
  body('rubricScores').optional().isObject()
], validate, updateGrade);

router.delete('/:id', [
  requirePermissions(['grade_submissions']),
  param('id').isUUID()
], validate, deleteGrade);

// Rubric-based grading routes
router.post('/rubric-based', [
  requirePermissions(['grade_submissions']),
  body('submissionId').isUUID().withMessage('Valid submission ID is required'),
  body('checkpointRubrics').isArray({ min: 1 }).withMessage('At least one checkpoint rubric is required'),
  body('checkpointRubrics.*.checkpointId').isUUID().withMessage('Valid checkpoint ID is required'),
  body('checkpointRubrics.*.feedback').optional().isString().withMessage('Checkpoint feedback must be a string'),
  body('checkpointRubrics.*.rubrics').isArray({ min: 1 }).withMessage('At least one rubric is required per checkpoint'),
  body('checkpointRubrics.*.rubrics.*.rubricId').isUUID().withMessage('Valid rubric ID is required'),
  body('checkpointRubrics.*.rubrics.*.criteriaScores').isArray({ min: 1 }).withMessage('At least one criteria score is required per rubric'),
  body('checkpointRubrics.*.rubrics.*.criteriaScores.*.criterionName').isString().notEmpty().withMessage('Criterion name is required'),
  body('checkpointRubrics.*.rubrics.*.criteriaScores.*.score').isFloat({ min: 0 }).withMessage('Criterion score must be a number greater than or equal to 0'),
  body('overallFeedback').optional().isString().withMessage('Overall feedback must be a string')
], validate, createRubricBasedGrade);

// Other routes
router.get('/queue', [
  requirePermissions(['grade_submissions']),
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
  query('courseId').optional().isUUID(),
  query('projectId').optional().isUUID()
], validate, getGradingQueue);

router.post('/bulk', [
  requirePermissions(['grade_submissions']),
  body('submissions').isArray({ min: 1 }),
  body('submissions.*.submissionId').isUUID(),
  body('submissions.*.totalScore').isFloat({ min: 0 }),
  body('submissions.*.maxScore').optional().isFloat({ min: 0 }),
  body('submissions.*.percentage').optional().isFloat({ min: 0, max: 100 }),
  body('submissions.*.letterGrade').optional().isString(),
  body('submissions.*.feedback').optional().isString(),
  body('submissions.*.rubricScores').optional().isObject()
], validate, bulkGradeSubmissions);

router.get('/submission/:submissionId/rubric-data', [
  requirePermissions(['grade_submissions']),
  param('submissionId').isUUID()
], validate, getSubmissionRubricData);

router.get('/submission/:submissionId/history', [
  requirePermissions(['view_grades']),
  param('submissionId').isUUID()
], validate, getSubmissionGradingHistory);

router.get('/submission/:submissionId/progress-details', [
  requirePermissions(['view_grades']),
  param('submissionId').isUUID().withMessage('Valid submission ID is required')
], validate, getSubmissionProgressDetails);

router.get('/project/:projectId/statistics', [
  requirePermissions(['view_grades']),
  param('projectId').isUUID()
], validate, getGradeStatistics);

router.get('/comprehensive-statistics', [
  requirePermissions(['view_grades']),
  query('courseId').optional().isUUID().withMessage('Course ID must be a valid UUID'),
  query('projectId').optional().isUUID().withMessage('Project ID must be a valid UUID'),
  query('search').optional().isString().withMessage('Search must be a string'),
  query('page').optional().isInt({ min: 1 }).toInt().withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt().withMessage('Limit must be between 1 and 100'),
  query('sortBy').optional().isIn(['name', 'email', 'completionPercentage', 'totalRubricCount', 'completedCount', 'pendingCount', 'latestSubmissionDate']),
  query('sortOrder').optional().isIn(['asc', 'desc'])
], validate, getComprehensiveGradeStatistics);

router.get('/student/:studentId/details', [
  requirePermissions(['view_grades']),
  param('studentId').isUUID().withMessage('Valid student ID is required')
], validate, getStudentDetails);

export default router;