// import express from 'express';
// import {
//   getCourses,
//   getCourseById,
//   getCourseEnrollments,
//   enrollUserInCourse,
//   updateCourseEnrollment,
//   getCourseStatistics
// } from '../controllers/course.controller.js';
// import { requirePermissions, requireRoles } from '../middlewares/rbac.middlewares.js';
// import { body, param, query } from 'express-validator';
// import { validate } from '../middlewares/validation.middlewares.js';

// const router = express.Router();

// /**
//  * @swagger
//  * tags:
//  *   name: Courses
//  *   description: Course management operations
//  */

// /**
//  * @swagger
//  * /api/courses:
//  *   get:
//  *     summary: Get all courses with pagination and filtering
//  *     tags: [Courses]
//  *     security:
//  *       - bearerAuth: []
//  *     parameters:
//  *       - in: query
//  *         name: page
//  *         schema:
//  *           type: integer
//  *           minimum: 1
//  *         description: Page number
//  *       - in: query
//  *         name: limit
//  *         schema:
//  *           type: integer
//  *           minimum: 1
//  *           maximum: 100
//  *         description: Number of items per page
//  *       - in: query
//  *         name: search
//  *         schema:
//  *           type: string
//  *         description: Search term for course name or code
//  *       - in: query
//  *         name: term
//  *         schema:
//  *           type: string
//  *         description: Filter by term
//  *       - in: query
//  *         name: status
//  *         schema:
//  *           type: string
//  *           enum: [active, inactive, archived, draft]
//  *         description: Filter by status
//  *     responses:
//  *       200:
//  *         description: Courses retrieved successfully
//  *       403:
//  *         description: Insufficient permissions
//  */
// router.get('/', [
//   requirePermissions(['view_courses']),
//   query('page').optional().isInt({ min: 1 }).toInt(),
//   query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
//   query('search').optional().trim(),
//   query('term').optional().trim(),
//   query('status').optional().isIn(['active', 'inactive', 'archived', 'draft'])
// ], validate, getCourses);

// /**
//  * @swagger
//  * /api/courses/{id}:
//  *   get:
//  *     summary: Get course by ID
//  *     tags: [Courses]
//  *     security:
//  *       - bearerAuth: []
//  *     parameters:
//  *       - in: path
//  *         name: id
//  *         required: true
//  *         schema:
//  *           type: string
//  *           format: uuid
//  *     responses:
//  *       200:
//  *         description: Course retrieved successfully
//  *       404:
//  *         description: Course not found
//  *       403:
//  *         description: Access denied
//  */
// router.get('/:id', [
//   param('id').isUUID()
// ], validate, getCourseById);

// /**
//  * @swagger
//  * /api/courses/{id}/enrollments:
//  *   get:
//  *     summary: Get course enrollments
//  *     tags: [Courses]
//  *     security:
//  *       - bearerAuth: []
//  *     parameters:
//  *       - in: path
//  *         name: id
//  *         required: true
//  *         schema:
//  *           type: string
//  *           format: uuid
//  *       - in: query
//  *         name: page
//  *         schema:
//  *           type: integer
//  *           minimum: 1
//  *         description: Page number
//  *       - in: query
//  *         name: limit
//  *         schema:
//  *           type: integer
//  *           minimum: 1
//  *           maximum: 100
//  *         description: Number of items per page
//  *       - in: query
//  *         name: search
//  *         schema:
//  *           type: string
//  *         description: Search term for user name or email
//  *       - in: query
//  *         name: role
//  *         schema:
//  *           type: string
//  *           enum: [student, instructor, ta]
//  *         description: Filter by role in course
//  *       - in: query
//  *         name: status
//  *         schema:
//  *           type: string
//  *           enum: [active, inactive, dropped, completed]
//  *         description: Filter by enrollment status
//  *     responses:
//  *       200:
//  *         description: Enrollments retrieved successfully
//  *       403:
//  *         description: Insufficient permissions
//  *       404:
//  *         description: Course not found
//  */
// router.get('/:id/enrollments', [
//   requirePermissions(['manage_enrollments']),
//   param('id').isUUID(),
//   query('page').optional().isInt({ min: 1 }).toInt(),
//   query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
//   query('search').optional().trim(),
//   query('role').optional().isIn(['student', 'instructor', 'ta']),
//   query('status').optional().isIn(['active', 'inactive', 'dropped', 'completed'])
// ], validate, getCourseEnrollments);

// /**
//  * @swagger
//  * /api/courses/{id}/enrollments:
//  *   post:
//  *     summary: Enroll user in course
//  *     tags: [Courses]
//  *     security:
//  *       - bearerAuth: []
//  *     parameters:
//  *       - in: path
//  *         name: id
//  *         required: true
//  *         schema:
//  *           type: string
//  *           format: uuid
//  *     requestBody:
//  *       required: true
//  *       content:
//  *         application/json:
//  *           schema:
//  *             type: object
//  *             required:
//  *               - userId
//  *             properties:
//  *               userId:
//  *                 type: string
//  *                 format: uuid
//  *               roleInCourse:
//  *                 type: string
//  *                 enum: [student, instructor, ta]
//  *                 default: student
//  *     responses:
//  *       201:
//  *         description: User enrolled successfully
//  *       400:
//  *         description: Validation error
//  *       403:
//  *         description: Insufficient permissions
//  *       404:
//  *         description: Course or user not found
//  *       409:
//  *         description: User already enrolled
//  */
// router.post('/:id/enrollments', [
//   requireRoles(['admin']),
//   param('id').isUUID(),
//   body('userId').isUUID(),
//   body('roleInCourse').optional().isIn(['student', 'instructor', 'ta'])
// ], validate, enrollUserInCourse);

// /**
//  * @swagger
//  * /api/courses/{id}/enrollments/{enrollmentId}:
//  *   put:
//  *     summary: Update course enrollment
//  *     tags: [Courses]
//  *     security:
//  *       - bearerAuth: []
//  *     parameters:
//  *       - in: path
//  *         name: id
//  *         required: true
//  *         schema:
//  *           type: string
//  *           format: uuid
//  *       - in: path
//  *         name: enrollmentId
//  *         required: true
//  *         schema:
//  *           type: string
//  *           format: uuid
//  *     requestBody:
//  *       required: true
//  *       content:
//  *         application/json:
//  *           schema:
//  *             type: object
//  *             properties:
//  *               roleInCourse:
//  *                 type: string
//  *                 enum: [student, instructor, ta]
//  *               enrollmentStatus:
//  *                 type: string
//  *                 enum: [active, inactive, dropped, completed]
//  *               finalGrade:
//  *                 type: number
//  *                 minimum: 0
//  *                 maximum: 100
//  *     responses:
//  *       200:
//  *         description: Enrollment updated successfully
//  *       400:
//  *         description: Validation error
//  *       403:
//  *         description: Insufficient permissions
//  *       404:
//  *         description: Enrollment not found
//  */
// router.put('/:id/enrollments/:enrollmentId', [
//   requirePermissions(['manage_enrollments']),
//   param('id').isUUID(),
//   param('enrollmentId').isUUID(),
//   body('roleInCourse').optional().isIn(['student', 'instructor', 'ta']),
//   body('enrollmentStatus').optional().isIn(['active', 'inactive', 'dropped', 'completed']),
//   body('finalGrade').optional().isFloat({ min: 0, max: 100 })
// ], validate, updateCourseEnrollment);

// /**
//  * @swagger
//  * /api/courses/{id}/statistics:
//  *   get:
//  *     summary: Get course statistics
//  *     tags: [Courses]
//  *     security:
//  *       - bearerAuth: []
//  *     parameters:
//  *       - in: path
//  *         name: id
//  *         required: true
//  *         schema:
//  *           type: string
//  *           format: uuid
//  *     responses:
//  *       200:
//  *         description: Statistics retrieved successfully
//  *       403:
//  *         description: Insufficient permissions
//  *       404:
//  *         description: Course not found
//  */
// router.get('/:id/statistics', [
//   requirePermissions(['view_courses']),
//   param('id').isUUID()
// ], validate, getCourseStatistics);

// export default router;