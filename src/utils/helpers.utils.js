import crypto from 'crypto';
import { v4 as uuidv4 } from 'uuid';
import lodash from 'lodash';
import httpStatus from 'http-status';
import logger from '../config/logger.config.js';

export const response = async (code, data, message) => {
  return {
    status: code,
    response: {
      isSuccess: true,
      message,
      data
    }
  };
};

export const errorResponse = async (code, message, err) => {
  return {
    status: code,
    response: {
      isSuccess: false,
      message,
      error: err
    }
  };
};

function _getLogDetails(req, infoMsg, component, statusCode, stack) {
  const url = lodash.get(req, 'originalUrl', '');
  const UID = lodash.get(req, 'user.id') || 'N/A';
  const userAgent =
    lodash.get(req, 'headersDistinct.user-agent[0]') ||
    lodash.get(req, 'user-agent') ||
    lodash.get(req, '_userAgent', 'N/A');
  const clientIp =
    lodash.get(req, 'x-client-ip', '') || lodash.get(req, 'ip', 'N/A');
  const email = lodash.get(req, 'user.email') || lodash.get(req, 'body.email');
  // const sessionID = lodash.get(req, "decoded.sid", "");
  const module_name = 'Bits DS Projects Portal';

  let fileName, lineNumber, methodNameStr;
  if (stack) {
    // Extract file name and line number from the stack trace
    const stackLine = filePath(stack) || '';
    const fileInfo = stackLine ? stackLine.match(/\((.*):(\d+):\d+\)/) : null;
    fileName = fileInfo ? fileInfo[1] : '';
    lineNumber = fileInfo ? fileInfo[2] : '';

    // Extract method name
    const methodLine = stackLine;
    const methodName = methodLine ? methodLine.match(/at (.*) \(/) : null;
    methodNameStr = methodName ? methodName[1] : '';
  }
  const messageInfo = `${infoMsg}`;

  let requestData = {};
  let requestDetails = '';
  let source = '';

  if (lodash.isPlainObject(req?.body) && Object.keys(req.body).length > 0) {
    requestDetails = lodash.get(req, 'body');
    source = 'body';
  } else if (
    lodash.isPlainObject(req?.query) &&
    Object.keys(req.query).length > 0
  ) {
    requestDetails = lodash.get(req, 'query');
    source = 'query';
  } else if (
    lodash.isPlainObject(req?.params) &&
    Object.keys(req.params).length > 0
  ) {
    requestDetails = lodash.get(req, 'params');
    source = 'params';
  }
  requestData = `${source}:${JSON.stringify(requestDetails)}`;

  return {
    message: messageInfo,
    component,
    userId: UID,
    clientIp,
    userAgent,
    url,
    fileName,
    lineNumber,
    methodNameStr,
    requestData,
    statusCode,
    stack,
    // sessionID,
    module_name,
    email
  };
}

const filePath = stack => {
  const stackLine = stack.split('\n');
  let result;
  for (let i = 0; i < stackLine.length; i++) {
    if (
      !stackLine[i].match(/\((.*?node_modules.*?)\)/g) &&
      !stackLine[i].match(/\bnode:[^\s()]+/g) &&
      stackLine[i].match(/\((.*):(\d+):\d+\)/)
    ) {
      result = stackLine[i];
      break;
    }
  }
  return result;
};

function getLogDetails(req, infoMsg, component, response) {
  const url = lodash.get(req, 'originalUrl', '');
  const UID =
    lodash.get(req, 'user.id') ||
    lodash.get(req, 'body.userId') ||
    lodash.get(req, 'body.account_id') ||
    lodash.get(req, 'account_id', 'N/A');
  const userAgent =
    lodash.get(req, 'headersDistinct.user-agent[0]') ||
    lodash.get(req, 'user-agent') ||
    lodash.get(req, '_userAgent', 'N/A');
  const clientIp =
    lodash.get(req, 'x-client-ip', '') || lodash.get(req, 'ip', 'N/A');
  const messageInfo = `${infoMsg}`;
  // const sessionID = lodash.get(req, "decoded.sid", "");

  let requestData = {};
  let requestDetails = '';
  let source = '';

  if (lodash.isPlainObject(req?.body) && Object.keys(req.body).length > 0) {
    requestDetails = lodash.get(req, 'body');
    source = 'body';
  } else if (
    lodash.isPlainObject(req?.query) &&
    Object.keys(req.query).length > 0
  ) {
    requestDetails = lodash.get(req, 'query');
    source = 'query';
  } else if (
    lodash.isPlainObject(req?.params) &&
    Object.keys(req.params).length > 0
  ) {
    requestDetails = lodash.get(req, 'params');
    source = 'params';
  }
  requestData = `${source}:${JSON.stringify(requestDetails)}`;

  return {
    message: messageInfo,
    component,
    userId: UID,
    clientIp,
    userAgent,
    url,
    requestData,
    responseData: response || {}
  };
}

export const LoggerInfo = (req, infoMsg, component, response) => {
  const logObj = getLogDetails(req, infoMsg, component, response);
  logger.info(logObj);
};

export const LoggerError = (req, infoMsg, component, statusCode, stack) => {
  const logObj = _getLogDetails(req, infoMsg, component, statusCode, stack);
  logger.error(logObj);
};

export const buildErrorResponse = async (
  req,
  error,
  auditComponent,
  errComponent
) => {
  const ApiErrRes = {
    msg:
      error && error.isOperational
        ? error.message || 'Something went wrong, please try again later'
        : 'Something went wrong, please try again later',
    status:
      error && error.isOperational
        ? error.statusCode || httpStatus.INTERNAL_SERVER_ERROR
        : httpStatus.INTERNAL_SERVER_ERROR
  };
  // addAuditLog(req, ApiErrRes, auditComponent, ApiErrRes.msg);
  LoggerError(
    req,
    error.message || ApiErrRes.msg,
    errComponent,
    ApiErrRes.status,
    error.stack
  );
  const errorResponseData = await errorResponse(
    ApiErrRes.status,
    ApiErrRes.msg
  );
  return errorResponseData;
};

export const buildSuccessResponse = async (
  req,
  res,
  responseData,
  infoMsg,
  sucComponent,
  auditComponent,
  statusCode
) => {
  // addAuditLog(req, ApiErrRes, auditComponent, ApiErrRes.msg);
  LoggerInfo(req, infoMsg, sucComponent, responseData);
  const responseDetails = await response(
    statusCode || httpStatus.OK,
    responseData,
    infoMsg
  );
  return res.status(responseDetails.status).json(responseDetails.response);
};

/**
 * Generate a random string of specified length
 * @param {number} length - Length of the string to generate
 * @returns {string} Random string
 */
export const generateRandomString = (length = 32) => {
  return crypto.randomBytes(length).toString('hex');
};

/**
 * Generate a secure token
 * @param {number} length - Length of the token
 * @returns {string} Secure token
 */
export const generateSecureToken = (length = 64) => {
  return crypto.randomBytes(length).toString('base64url');
};

/**
 * Generate a UUID
 * @returns {string} UUID
 */
export const generateUUID = () => {
  return uuidv4();
};

/**
 * Hash a string using SHA-256
 * @param {string} input - String to hash
 * @returns {string} Hashed string
 */
export const hashString = input => {
  return crypto.createHash('sha256').update(input).digest('hex');
};

/**
 * Validate email format
 * @param {string} email - Email to validate
 * @returns {boolean} True if valid email
 */
export const isValidEmail = email => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate UUID format
 * @param {string} uuid - UUID to validate
 * @returns {boolean} True if valid UUID
 */
export const isValidUUID = uuid => {
  const uuidRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
};

/**
 * Sanitize string input
 * @param {string} input - Input to sanitize
 * @returns {string} Sanitized string
 */
export const sanitizeString = input => {
  if (typeof input !== 'string') return '';
  return input.trim().replace(/[<>]/g, '');
};

/**
 * Format file size in human readable format
 * @param {number} bytes - Size in bytes
 * @returns {string} Formatted size
 */
export const formatFileSize = bytes => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Format date to ISO string
 * @param {Date|string} date - Date to format
 * @returns {string} ISO formatted date
 */
export const formatDate = date => {
  if (!date) return null;
  return new Date(date).toISOString();
};

/**
 * Get pagination parameters
 * @param {Object} query - Query parameters
 * @param {number} defaultLimit - Default limit
 * @param {number} maxLimit - Maximum limit
 * @returns {Object} Pagination object
 */
export const getPaginationParams = (
  query,
  defaultLimit = 10,
  maxLimit = 100
) => {
  const page = Math.max(1, parseInt(query.page) || 1);
  const limit = Math.min(
    maxLimit,
    Math.max(1, parseInt(query.limit) || defaultLimit)
  );
  const offset = (page - 1) * limit;

  return { page, limit, offset };
};

/**
 * Create pagination response
 * @param {Array} data - Data array
 * @param {number} page - Current page
 * @param {number} limit - Items per page
 * @param {number} total - Total items
 * @returns {Object} Pagination response
 */
export const createPaginationResponse = (data, page, limit, total) => {
  const totalPages = Math.ceil(total / limit);

  return {
    data,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    }
  };
};

/**
 * Deep clone an object
 * @param {*} obj - Object to clone
 * @returns {*} Cloned object
 */
export const deepClone = obj => {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map(item => deepClone(item));
  if (typeof obj === 'object') {
    const clonedObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
};

/**
 * Check if object is empty
 * @param {Object} obj - Object to check
 * @returns {boolean} True if empty
 */
export const isEmpty = obj => {
  if (obj == null) return true;
  if (Array.isArray(obj) || typeof obj === 'string') return obj.length === 0;
  return Object.keys(obj).length === 0;
};

/**
 * Pick specific properties from an object
 * @param {Object} obj - Source object
 * @param {Array} keys - Keys to pick
 * @returns {Object} Object with picked properties
 */
export const pick = (obj, keys) => {
  const result = {};
  keys.forEach(key => {
    if (obj.hasOwnProperty(key)) {
      result[key] = obj[key];
    }
  });
  return result;
};

/**
 * Omit specific properties from an object
 * @param {Object} obj - Source object
 * @param {Array} keys - Keys to omit
 * @returns {Object} Object without omitted properties
 */
export const omit = (obj, keys) => {
  const result = {};
  Object.keys(obj).forEach(key => {
    if (!keys.includes(key)) {
      result[key] = obj[key];
    }
  });
  return result;
};

/**
 * Sleep for specified milliseconds
 * @param {number} ms - Milliseconds to sleep
 * @returns {Promise} Promise that resolves after sleep
 */
export const sleep = ms => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Retry a function with exponential backoff
 * @param {Function} fn - Function to retry
 * @param {number} maxRetries - Maximum number of retries
 * @param {number} baseDelay - Base delay in milliseconds
 * @returns {Promise} Promise that resolves with function result
 */
export const retryWithBackoff = async (
  fn,
  maxRetries = 3,
  baseDelay = 1000
) => {
  let lastError;

  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      if (i === maxRetries) break;

      const delay = baseDelay * Math.pow(2, i);
      await sleep(delay);
    }
  }

  throw lastError;
};

/**
 * Validate password strength
 * @param {string} password - Password to validate
 * @returns {Object} Validation result
 */
export const validatePasswordStrength = password => {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

  const isValid =
    password.length >= minLength &&
    hasUpperCase &&
    hasLowerCase &&
    hasNumbers &&
    hasSpecialChar;

  return {
    isValid,
    errors: {
      tooShort: password.length < minLength,
      noUpperCase: !hasUpperCase,
      noLowerCase: !hasLowerCase,
      noNumbers: !hasNumbers,
      noSpecialChar: !hasSpecialChar
    }
  };
};

/**
 * Generate initials from name
 * @param {string} name - Full name
 * @returns {string} Initials
 */
export const getInitials = name => {
  if (!name) return '';
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

/**
 * Truncate text to specified length
 * @param {string} text - Text to truncate
 * @param {number} maxLength - Maximum length
 * @param {string} suffix - Suffix to add if truncated
 * @returns {string} Truncated text
 */
export const truncateText = (text, maxLength = 100, suffix = '...') => {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength - suffix.length) + suffix;
};

/**
 * Convert string to title case
 * @param {string} str - String to convert
 * @returns {string} Title case string
 */
export const toTitleCase = str => {
  if (!str) return '';
  return str.replace(
    /\w\S*/g,
    txt => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
  );
};

/**
 * Generate a slug from string
 * @param {string} str - String to slugify
 * @returns {string} Slug
 */
export const slugify = str => {
  if (!str) return '';
  return str
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
};

/**
 * Check if value is a valid URL
 * @param {string} url - URL to validate
 * @returns {boolean} True if valid URL
 */
export const isValidURL = url => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * Extract domain from URL
 * @param {string} url - URL to extract domain from
 * @returns {string} Domain
 */
export const extractDomain = url => {
  try {
    return new URL(url).hostname;
  } catch {
    return '';
  }
};

/**
 * Generate a random color hex
 * @returns {string} Color hex
 */
export const generateRandomColor = () => {
  return '#' + Math.floor(Math.random() * 16777215).toString(16);
};

/**
 * Check if two arrays are equal
 * @param {Array} arr1 - First array
 * @param {Array} arr2 - Second array
 * @returns {boolean} True if arrays are equal
 */
export const arraysEqual = (arr1, arr2) => {
  if (arr1.length !== arr2.length) return false;
  return arr1.every((val, index) => val === arr2[index]);
};

/**
 * Remove duplicates from array
 * @param {Array} arr - Array to deduplicate
 * @returns {Array} Deduplicated array
 */
export const removeDuplicates = arr => {
  return [...new Set(arr)];
};

/**
 * Group array by key
 * @param {Array} arr - Array to group
 * @param {string} key - Key to group by
 * @returns {Object} Grouped object
 */
export const groupBy = (arr, key) => {
  return arr.reduce((groups, item) => {
    const group = item[key];
    groups[group] = groups[group] || [];
    groups[group].push(item);
    return groups;
  }, {});
};

/**
 * Sort array by multiple keys
 * @param {Array} arr - Array to sort
 * @param {Array} keys - Array of key objects with key and order
 * @returns {Array} Sorted array
 */
export const sortByMultiple = (arr, keys) => {
  return arr.sort((a, b) => {
    for (const { key, order = 'asc' } of keys) {
      const aVal = a[key];
      const bVal = b[key];

      if (aVal < bVal) return order === 'asc' ? -1 : 1;
      if (aVal > bVal) return order === 'asc' ? 1 : -1;
    }
    return 0;
  });
};

export const checkAccessRole = async (primaryRole, userRoles, checkRoles) => {
  let ALLOW = new Set([
    'admin',
    'superadmin',
    'super-admin',
    'instructor',
    'student'
  ]);
  if (checkRoles === 'adminOnly')
    ALLOW = new Set(['admin', 'superadmin', 'super-admin']);
  if (checkRoles === 'instructorOnly') ALLOW = new Set(['instructor']);
  if (checkRoles === 'adminInstructor')
    ALLOW = new Set(['admin', 'superadmin', 'super-admin', 'instructor']);
  if (checkRoles === 'allRoles')
    ALLOW = new Set([
      'admin',
      'superadmin',
      'super-admin',
      'instructor',
      'student'
    ]);
  if (checkRoles === 'taOnly') ALLOW = new Set(['ta']);
  if (checkRoles === 'taInstructor') ALLOW = new Set(['ta', 'instructor']);
  if (checkRoles === 'studentOnly') ALLOW = new Set(['student']);

  const primary = String(primaryRole || '').toLowerCase();
  const hasAllowedRole = (userRoles ?? []).some(r =>
    ALLOW.has(String(r).toLowerCase())
  );

  if (!ALLOW.has(primary) && !hasAllowedRole) {
    return false;
  }
  return true;
};

export const getSortOrderList = (
  sortBy,
  orderBy,
  defaultSort,
  sqlQuerySort
) => {
  const attributesIndex = {};

  if (sqlQuerySort) {
    const { attributes = [], replaceFields = [] } = sqlQuerySort || {};

    if (replaceFields.length > 0) {
      attributes.forEach(ele => {
        if (typeof ele === 'object') {
          const [sQ, aliasName] = ele;
          if (replaceFields.includes(aliasName))
            attributesIndex[aliasName] = sQ;
        }
      });
    }
  }

  const sorting = [];
  if (sortBy && orderBy) {
    const sortByList = sortBy.split(',');
    const orderByList = orderBy.split(',');

    for (let i = 0; i < sortByList.length; i += 1) {
      const sortList = [];
      sortList.push(attributesIndex[sortByList[i]] || sortByList[i]);
      sortList.push(orderByList[i]);
      sorting.push(sortList);
    }
  } else if (defaultSort && defaultSort.length > 0) {
    return defaultSort;
  }
  return sorting;
};
/**
 * Validate start_date and end_date
 * @param {string|Date} start_date - Start date (string or Date)
 * @param {string|Date} end_date - End date (string or Date)
 * @returns {{ valid: boolean, message: string }}
 */
export const validateDates = (start_date, end_date) => {
  if (!start_date || !end_date) {
    return {
      valid: false,
      message: 'Both start_date and end_date are required.'
    };
  }

  const start = new Date(start_date);
  const end = new Date(end_date);
  const today = new Date();
  today.setHours(0, 0, 0, 0); // remove time part

  if (isNaN(start) || isNaN(end)) {
    return { valid: false, message: 'Invalid date format.' };
  }

  if (start > end) {
    return { valid: false, message: 'start_date cannot be after end_date.' };
  }

  if (start < today) {
    return { valid: false, message: 'start_date cannot be in the past.' };
  }

  if (end < today) {
    return { valid: false, message: 'end_date cannot be in the past.' };
  }

  // Max duration: 1 year
  const oneYearLater = new Date(start);
  oneYearLater.setFullYear(start.getFullYear() + 1);

  if (end > oneYearLater) {
    return {
      valid: false,
      message: 'end_date cannot be more than 1 year after start_date.'
    };
  }

  return { valid: true, message: 'Dates are valid.' };
};

export default {
  generateRandomString,
  generateSecureToken,
  generateUUID,
  hashString,
  isValidEmail,
  isValidUUID,
  sanitizeString,
  formatFileSize,
  formatDate,
  getPaginationParams,
  createPaginationResponse,
  deepClone,
  isEmpty,
  pick,
  omit,
  sleep,
  retryWithBackoff,
  validatePasswordStrength,
  getInitials,
  truncateText,
  toTitleCase,
  slugify,
  isValidURL,
  extractDomain,
  generateRandomColor,
  arraysEqual,
  removeDuplicates,
  groupBy,
  sortByMultiple,
  checkAccessRole,
  errorResponse,
  response,
  buildErrorResponse,
  LoggerInfo,
  LoggerError,
  getSortOrderList,
  validateDates,
};
