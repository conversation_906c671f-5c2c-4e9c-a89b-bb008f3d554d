import { asyncHandler } from '../middlewares/errorHandler.middlewares.js';
import {
  Project,
  LtiContext,
  Checkpoint,
  Activity
} from '../models/associations.js';
import dashboardService from '../services/dashboard.service.js';
import activityLoggerService from '../services/activityLogger.service.js';
import logger from '../config/logger.config.js';

/**
 * @desc    Get instructor dashboard overview
 * @route   GET /api/instructor/dashboard
 * @access  Private (Instructor/Admin)
 */
export const getInstructorDashboard = asyncHandler(async (req, res) => {
  const { courseId } = req.query;

  // Check permissions
  const hasPermission =
    req.userRoles.includes('admin') || req.userRoles.includes('instructor');

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to view instructor dashboard'
    });
  }

  try {
    const dashboardData = await dashboardService.calculateDashboardStats(
      courseId,
      req.user.id
    );

    res.json({
      success: true,
      dashboard: dashboardData
    });
  } catch (error) {
    logger.error('Error getting instructor dashboard:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to load dashboard data'
    });
  }
});

/**
 * @desc    Get recent activity for instructor
 * @route   GET /api/instructor/activity
 * @access  Private (Instructor/Admin)
 */
export const getRecentActivity = asyncHandler(async (req, res) => {
  const { courseId, limit = 20, projectId } = req.query;

  // Check permissions
  const hasPermission =
    req.userRoles.includes('admin') || req.userRoles.includes('instructor');

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to view activity data'
    });
  }

  try {
    let activities;

    if (projectId) {
      // Get project-specific activity
      activities = await activityLoggerService.getProjectActivitySummary(
        projectId,
        parseInt(limit)
      );
    } else {
      // Get course-wide activity
      const courseIds = courseId ? [courseId] : null;
      activities = await dashboardService.getRecentActivity(
        courseIds,
        req.user.id,
        parseInt(limit)
      );
    }

    res.json({
      success: true,
      activities
    });
  } catch (error) {
    logger.error('Error getting recent activity:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to load activity data'
    });
  }
});

/**
 * @desc    Get enhanced project listing with statistics for instructor
 * @route   GET /api/instructor/projects
 * @access  Private (Instructor/Admin)
 */
export const getInstructorProjects = asyncHandler(async (req, res) => {
  const {
    courseId,
    status,
    page = 1,
    limit = 10,
    includeStats = 'true'
  } = req.query;

  // Check permissions
  const hasPermission =
    req.userRoles.includes('admin') || req.userRoles.includes('instructor');

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to view instructor projects'
    });
  }

  try {
    // Build where clause for course filtering
    const courseWhere = courseId ? { id: courseId } : {};
    const instructorWhere = { instructor_id: req.user.id };

    // Get courses for the instructor
    const courses = await LtiContext.findAll({
      where: { ...courseWhere, ...instructorWhere },
      attributes: ['id', ['context_title', 'name'], ['context_label', 'code']],
      raw: true
    });

    if (courses.length === 0) {
      return res.json({
        success: true,
        data: {
          projects: [],
          dashboardStats: {},
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: 0,
            pages: 0
          }
        }
      });
    }

    const courseIds = courses.map(c => c.id);

    // Build project where clause
    const projectWhere = {
      course_id: { [require('sequelize').Op.in]: courseIds }
    };
    if (status) {
      projectWhere.status = status;
    }

    // Get projects with enhanced data
    const offset = (parseInt(page) - 1) * parseInt(limit);

    const { count, rows: projects } = await Project.findAndCountAll({
      where: projectWhere,
      include: [
        {
          model: LtiContext,
          as: 'course',
          attributes: [
            'id',
            ['context_title', 'name'],
            ['context_label', 'code']
          ],
          raw: true
        },
        {
          model: Checkpoint,
          as: 'checkpoints',
          where: { status: 'published' },
          required: false,
          attributes: ['id', 'checkpoint_number', 'weight_percentage']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset
    });

    // Calculate dashboard statistics
    const dashboardStats = await dashboardService.calculateDashboardStats(
      courseId,
      req.user.id
    );

    // Transform projects with progress data if stats are requested
    let transformedProjects = projects;

    if (includeStats === 'true') {
      transformedProjects = await Promise.all(
        projects.map(async project => {
          const projectStats = await dashboardService.calculateProjectStats(
            project.id
          );

          return {
            id: project.id,
            title: project.title,
            description: project.description,
            status: project.status,
            startDate: project.start_date || project.created_at,
            dueDate: project.due_date,
            progressPercentage: projectStats.progressPercentage,
            totalStudents: projectStats.totalStudents,
            activeStudents: projectStats.activeStudents,
            submissionsCount: projectStats.submissionsCount,
            gradedCount: projectStats.gradedCount,
            pendingGrades: projectStats.pendingGrades,
            averageGrade: projectStats.averageGrade,
            checkpoints: project.checkpoints?.length || 0,
            course: project.course,
            createdAt: project.created_at,
            updatedAt: project.updated_at
          };
        })
      );
    }

    const totalPages = Math.ceil(count / parseInt(limit));

    res.json({
      success: true,
      data: {
        projects: transformedProjects,
        dashboardStats,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: count,
          pages: totalPages
        }
      }
    });
  } catch (error) {
    logger.error('Error getting instructor projects:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to load project data'
    });
  }
});

/**
 * @desc    Get detailed project statistics for instructor
 * @route   GET /api/instructor/projects/:id/stats
 * @access  Private (Instructor/Admin)
 */
export const getProjectDetailedStats = asyncHandler(async (req, res) => {
  const { id: projectId } = req.params;

  // Check permissions
  const hasPermission =
    req.userRoles.includes('admin') || req.userRoles.includes('instructor');

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to view project statistics'
    });
  }

  try {
    const projectStats = await dashboardService.getProjectDetailedStats(
      projectId,
      req.user.id
    );

    res.json({
      success: true,
      projectStats
    });
  } catch (error) {
    logger.error('Error getting project detailed stats:', error);

    if (error.message === 'Project not found or access denied') {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Project not found or access denied'
      });
    }

    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to load project statistics'
    });
  }
});

/**
 * @desc    Get course overview for instructor
 * @route   GET /api/instructor/courses/:id/overview
 * @access  Private (Instructor/Admin)
 */
export const getCourseOverview = asyncHandler(async (req, res) => {
  const { id: courseId } = req.params;

  // Check permissions
  const hasPermission =
    req.userRoles.includes('admin') || req.userRoles.includes('instructor');

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to view course overview'
    });
  }

  try {
    // Verify instructor has access to this course
    const course = await LtiContext.findOne({
      where: {
        attributes: [
          'id',
          ['context_title', 'name'],
          ['context_label', 'code']
        ],
        raw: true
      }
    });

    if (!course) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Course not found or access denied'
      });
    }

    const courseOverview = await dashboardService.calculateCourseOverview([
      courseId
    ]);

    if (courseOverview.length === 0) {
      return res.status(404).json({
        error: 'Not Found',
        message: 'Course overview not available'
      });
    }

    res.json({
      success: true,
      courseOverview: courseOverview[0]
    });
  } catch (error) {
    logger.error('Error getting course overview:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to load course overview'
    });
  }
});

/**
 * @desc    Get user activity summary for instructor
 * @route   GET /api/instructor/users/:id/activity
 * @access  Private (Instructor/Admin)
 */
export const getUserActivitySummary = asyncHandler(async (req, res) => {
  const { id: userId } = req.params;
  const { limit = 50 } = req.query;

  // Check permissions
  const hasPermission =
    req.userRoles.includes('admin') || req.userRoles.includes('instructor');

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to view user activity'
    });
  }

  try {
    const activities = await activityLoggerService.getUserActivitySummary(
      userId,
      parseInt(limit)
    );

    res.json({
      success: true,
      activities
    });
  } catch (error) {
    logger.error('Error getting user activity summary:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to load user activity data'
    });
  }
});

/**
 * @desc    Get activity analytics for instructor
 * @route   GET /api/instructor/analytics
 * @access  Private (Instructor/Admin)
 */
export const getActivityAnalytics = asyncHandler(async (req, res) => {
  const { courseId, startDate, endDate, activityType } = req.query;

  // Check permissions
  const hasPermission =
    req.userRoles.includes('admin') || req.userRoles.includes('instructor');

  if (!hasPermission) {
    return res.status(403).json({
      error: 'Access Denied',
      message: 'You do not have permission to view analytics'
    });
  }

  try {
    // Build where clause
    const whereClause = {};

    if (courseId) {
      whereClause.course_id = courseId;
    }

    if (startDate && endDate) {
      whereClause.created_at = {
        [require('sequelize').Op.between]: [
          new Date(startDate),
          new Date(endDate)
        ]
      };
    }

    if (activityType) {
      whereClause.activity_type = activityType;
    }

    // Get activity counts by type
    const activityCounts = await Activity.findAll({
      where: whereClause,
      attributes: [
        'activity_type',
        [
          require('sequelize').fn('COUNT', require('sequelize').col('id')),
          'count'
        ]
      ],
      group: ['activity_type'],
      order: [
        [
          require('sequelize').fn('COUNT', require('sequelize').col('id')),
          'DESC'
        ]
      ]
    });

    // Get daily activity for the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const dailyActivity = await Activity.findAll({
      where: {
        ...whereClause,
        created_at: {
          [require('sequelize').Op.gte]: thirtyDaysAgo
        }
      },
      attributes: [
        [
          require('sequelize').fn(
            'DATE',
            require('sequelize').col('created_at')
          ),
          'date'
        ],
        [
          require('sequelize').fn('COUNT', require('sequelize').col('id')),
          'count'
        ]
      ],
      group: [
        require('sequelize').fn('DATE', require('sequelize').col('created_at'))
      ],
      order: [
        [
          require('sequelize').fn(
            'DATE',
            require('sequelize').col('created_at')
          ),
          'ASC'
        ]
      ]
    });

    const analytics = {
      activityCounts: activityCounts.map(item => ({
        type: item.getDataValue('activity_type'),
        count: parseInt(item.getDataValue('count'))
      })),
      dailyActivity: dailyActivity.map(item => ({
        date: item.getDataValue('date'),
        count: parseInt(item.getDataValue('count'))
      })),
      totalActivities: activityCounts.reduce(
        (sum, item) => sum + parseInt(item.getDataValue('count')),
        0
      )
    };

    res.json({
      success: true,
      analytics
    });
  } catch (error) {
    logger.error('Error getting activity analytics:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to load analytics data'
    });
  }
});
