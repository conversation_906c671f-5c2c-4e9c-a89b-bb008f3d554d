import { asyncHandler } from '../middlewares/errorHandler.middlewares.js';
import rubricService from '../services/rubric.service.js';
import { buildSuccessResponse } from '../utils/helpers.utils.js';
import httpStatus from 'http-status';

let component, auditComponent;
/**
 * @desc    Create a new rubric
 * @route   POST /api/rubrics
 * @access  Private (Project Management permissions)
 */
export const createRubric = asyncHandler(
  async (req, res) => {
    component = 'createRubric';
    auditComponent = 'Create Rubric';
    const result = await rubricService.creationOfRubric(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Rubric created successfully',
      component,
      auditComponent,
      httpStatus.CREATED
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Get all rubrics with filtering
 * @route   GET /api/rubrics
 * @access  Private (Project Management permissions)
 */
export const getRubrics = asyncHandler(
  async (req, res) => {
    component = 'getRubrics';
    auditComponent = 'Get Rubrics';
    const result = await rubricService.getRubrics(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Rubrics retrieved successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Get a specific rubric
 * @route   GET /api/rubrics/:id
 * @access  Private (Project Management permissions)
 */
export const getRubricById = asyncHandler(
  async (req, res) => {
    component = 'getRubricById';
    auditComponent = 'Get Rubric By ID';
    const result = await rubricService.getRubricById(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Rubric retrieved successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Update a rubric
 * @route   PUT /api/rubrics/:id
 * @access  Private (Project Management permissions)
 */
export const updateRubric = asyncHandler(
  async (req, res) => {
    component = 'updateRubric';
    auditComponent = 'Update Rubric';
    const result = await rubricService.updateRubric(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Rubric updated successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Bulk update rubrics for a project
 * @route   PUT /api/rubrics/bulk-update
 * @access  Private (Project Management permissions)
 */
export const bulkUpdateRubrics = asyncHandler(
  async (req, res) => {
    component = 'bulkUpdateRubrics';
    auditComponent = 'Bulk Update Rubrics';
    const result = await rubricService.bulkUpdateRubrics(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Rubrics updated successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Delete a rubric
 * @route   DELETE /api/rubrics/:id
 * @access  Private (Project Management permissions)
 */
export const deleteRubric = asyncHandler(
  async (req, res) => {
    component = 'deleteRubric';
    auditComponent = 'Delete Rubric';
    const result = await rubricService.deleteRubric(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Rubric deleted successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Get rubric templates
 * @route   GET /api/rubrics/templates
 * @access  Private (Project Management permissions)
 */
export const getRubricTemplates = asyncHandler(
  async (req, res) => {
    component = 'getRubricTemplates';
    auditComponent = 'Get Rubric Templates';
    const result = await rubricService.getRubricTemplates(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Rubric templates retrieved successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Duplicate a rubric template for a project
 * @route   POST /api/rubrics/:id/duplicate
 * @access  Private (Project Management permissions)
 */
export const duplicateRubricTemplate = asyncHandler(
  async (req, res) => {
    component = 'duplicateRubricTemplate';
    auditComponent = 'Duplicate Rubric Template';
    const result = await rubricService.duplicateRubricTemplate(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Rubric template duplicated successfully',
      component,
      auditComponent,
      httpStatus.CREATED
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Get rubrics for a specific project
 * @route   GET /api/rubrics/project/:projectId
 * @access  Private (Project Management permissions)
 */
export const getProjectRubrics = asyncHandler(
  async (req, res) => {
    component = 'getProjectRubrics';
    auditComponent = 'Get Project Rubrics';
    const result = await rubricService.getProjectRubrics(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Project rubrics retrieved successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);

/**
 * @desc    Get rubric statistics
 * @route   GET /api/rubrics/stats
 * @access  Private (Project Management permissions)
 */
export const getRubricStats = asyncHandler(
  async (req, res) => {
    component = 'getRubricStats';
    auditComponent = 'Get Rubric Statistics';
    const result = await rubricService.getRubricStats(req);
    await buildSuccessResponse(
      req,
      res,
      result,
      'Rubric statistics retrieved successfully',
      component,
      auditComponent
    );
  },
  { component, auditComponent }
);
