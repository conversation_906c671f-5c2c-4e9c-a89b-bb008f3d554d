import { asyncHandler } from '../middlewares/errorHandler.middlewares.js';
import ltiService, {
  parseLineItemByResourceLinkId,
  parseRosterMembers,
  parseLineItemsFromLaunch,
  parseStudentsFromNRPS,
  parseInstructorsFromNRPS,
  createLineItemFromSession,
  postScoreFromSubmission,
  listResultsForLineItem,
  getMyResultForLineItem,
  getLTIServiceStatusSummary,
  getLTIConfigurationData,
  fetchPlatformsData,
  updatePlatformById,
  deletePlatformById,
  registerPlatformData,
  fetchCourseContextsData,
  fetchAllContextsData,
  handleDeepLinkingRequest,
  sendGradeToLMSUsingIds,
  linkProjectWithLmsLineItem,
  getResourceLinksWithoutProject,
  fetchContextMembersData
} from '../services/lti.service.js';
import {
  LtiPlatform,
  LtiDeployment,
  LtiContext,
  LtiResourceLink,
  LtiLineItem,
  LtiLaunchSession
} from '../models/ltiAssociations.models.js';
import { User, Role, Permission, Project } from '../models/associations.js';
import logger from '../config/logger.config.js';
import axios from 'axios';
import { buildSuccessResponse } from '../utils/helpers.utils.js';
import httpStatus from 'http-status';
import jwtService from '../services/jwt.service.js';
import sessionCacheService from '../services/sessionCache.service.js';

let component;
let auditComponent;

/**
 * @desc    LTI login initiation endpoint
 * @route   POST /api/lti/login
 * @access  Public
 */
export const initiateLogin = asyncHandler(async (req, res) => {
  const { iss, login_hint, target_link_uri, lti_message_hint, client_id } =
    req.body;

  // Find platform by issuer
  const platform = await LtiPlatform.findOne({
    where: { platformId: iss, clientId: client_id, isActive: true }
  });

  if (!platform) {
    return res.status(400).json({
      error: 'Invalid Platform',
      message: 'Platform not registered or inactive'
    });
  }

  try {
    // Generate authentication request
    const authUrl = ltiService.generateAuthRequest(
      platform,
      target_link_uri,
      login_hint,
      lti_message_hint
    );

    // Redirect to platform authentication
    res.redirect(authUrl);
  } catch (error) {
    logger.error('LTI login initiation failed:', error);
    res.status(500).json({
      error: 'Login Failed',
      message: 'Could not initiate LTI login'
    });
  }
});

/**
 * @desc    LTI OIDC callback endpoint
 * @route   POST /api/lti/oidc/callback
 * @access  Public
 */
export const handleOIDCCallback = asyncHandler(async (req, res) => {
  const { id_token, state } = req.body;

  if (!id_token || !state) {
    return res.status(400).json({
      error: 'Missing Parameters',
      message: 'id_token and state are required'
    });
  }

  try {
    logger.info('[LTI] handleOIDCCallback: Looking for launch session');

    // Find launch session by state
    const session = await LtiLaunchSession.findOne({
      where: { state, isUsed: false },
      include: [{ model: LtiPlatform, as: 'platform' }]
    });

    if (!session || new Date() > session.expiresAt) {
      throw new Error('Invalid or expired launch session');
    }

    // Verify JWT token
    const launchData = await ltiService.verifyJWT(id_token, session.platform);

    // Validate nonce
    if (launchData.nonce !== session.nonce) {
      throw new Error('Nonce mismatch');
    }

    // Process launch data
    const processedData = await ltiService.processLaunchData(
      launchData,
      session.platform
    );

    // Mark session as used
    await session.update({
      isUsed: true,
      userId: processedData.user.id,
      idToken: id_token,
      launchData: { ...session.launchData, ...launchData }
    });

    // Determine the target page based on launch data
    const targetPage = await determineLaunchTarget(
      launchData,
      processedData.resourceLink,
      processedData.context.id,
      processedData.resourceLink.id
    );

    // Small delay to ensure role assignment is completed
    await new Promise(resolve => setTimeout(resolve, 100));

    // Fetch user with roles and permissions for session data
    const userWithRoles = await User.findByPk(processedData.user.id, {
      include: [
        {
          model: Role,
          as: 'roles',
          include: [
            {
              model: Permission,
              as: 'permissions'
            }
          ]
        }
      ],
      attributes: { exclude: ['password_hash'] }
    });

    logger.info('[LTI OIDC] User roles after fetch:', {
      userId: userWithRoles.id,
      roles: userWithRoles.roles?.map(r => ({ id: r.id, name: r.name })) || []
    });

    // Prepare session data for Redis storage
    const sessionData = {
      cookie: {
        originalMaxAge: 86400000,
        expires: new Date(Date.now() + 86400000).toISOString(),
        secure: process.env.NODE_ENV === 'production',
        httpOnly: true,
        path: '/'
      },
      user: {
        id: userWithRoles.id,
        name: userWithRoles.name,
        email: userWithRoles.email,
        lms_user_id: userWithRoles.lms_user_id,
        google_id: userWithRoles.google_id,
        profile_picture: userWithRoles.profile_picture,
        last_login: userWithRoles.last_login,
        status: userWithRoles.status,
        preferences: userWithRoles.preferences || {},
        metadata: userWithRoles.metadata || {},
        createdAt: userWithRoles.createdAt,
        updatedAt: userWithRoles.updatedAt,
        deletedAt: userWithRoles.deletedAt,
        roles: userWithRoles.roles || []
      },
      ltiContext: processedData.context,
      ltiResourceLink: processedData.resourceLink,
      ltiLaunchData: launchData, // Store the complete launch data
      sessionType: 'lti',
      createdAt: new Date().toISOString()
    };

    // Generate JWT tokens using user with roles
    const tokenPair = jwtService.generateTokenPair(userWithRoles, {
      sessionType: 'lti',
      ltiContext: processedData.context,
      ltiResourceLink: processedData.resourceLink
    });

    // Store session data in Redis
    await sessionCacheService.setUserSession(
      processedData.user.id,
      sessionData
    );

    // Store refresh token mapping
    await sessionCacheService.setRefreshToken(
      tokenPair.refreshToken,
      processedData.user.id
    );

    // Set refresh token as HttpOnly cookie
    res.cookie('refreshToken', tokenPair.refreshToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      path: '/'
    });

    console.log('LTI OIDC callback -- sessionData', { sessionData });
    console.log(
      'LTI OIDC callback -- launchData keys:',
      Object.keys(launchData)
    );
    console.log('LTI OIDC callback -- launchData sample:', {
      sub: launchData.sub,
      email: launchData.email,
      roles: launchData['https://purl.imsglobal.org/spec/lti/claim/roles']
    });

    // Redirect to the appropriate page with access token
    const redirectUrl = `${process.env.FRONTEND_URL}/${targetPage}`;

    logger.info(`[LTI] handleOIDCCallback: Redirecting to ${redirectUrl}`);
    res.redirect(redirectUrl);
  } catch (error) {
    logger.error('[LTI] handleOIDCCallback: Error:', error);
    res.status(400).json({
      error: 'OIDC Callback Failed',
      message: error.message
    });
  }
});

/**
 * @desc    LTI launch endpoint
 * @route   POST /api/lti/launch
 * @access  Public
 */
export const handleLaunch = asyncHandler(async (req, res) => {
  const { id_token, state } = req.body;

  if (!id_token || !state) {
    return res.status(400).json({
      error: 'Missing Parameters',
      message: 'id_token and state are required'
    });
  }

  try {
    // Process LTI launch
    const launchResult = await ltiService.processLaunch(id_token, state);

    // Determine the target page based on launch data
    const targetPage = determineLaunchTarget(
      launchResult.launchData,
      launchResult.resourceLink
    );

    // Create session for the user
    req.session.user = launchResult.user;
    req.session.ltiContext = launchResult.context;
    req.session.ltiResourceLink = launchResult.resourceLink;
    req.session.ltiLaunchData = launchResult.launchData;

    // Redirect to the appropriate page in the application
    const redirectUrl = `${process.env.FRONTEND_URL}/${targetPage}`;

    res.redirect(redirectUrl);
  } catch (error) {
    logger.error('LTI launch failed:', error);
    res.status(400).json({
      error: 'Launch Failed',
      message: error.message
    });
  }
});

/**
 * @desc    JWKS endpoint for tool public keys
 * @route   GET /api/lti/jwks
 * @access  Public
 */
export const getJWKS = asyncHandler(async (req, res) => {
  const jwks = ltiService.getJWKS();
  res.json(jwks);
});

/**
 * @desc    Deep linking endpoint
 * @route   POST /api/lti/deep-linking
 * @access  Public
 */
export const handleDeepLinking = asyncHandler(async (req, res) => {
  const { id_token, state } = req.body;
  const result = await handleDeepLinkingRequest(id_token, state);
  // Deep-linking requires an auto-posting HTML form back to the platform
  res.send(result.html);
});

/**
 * @desc    Register new LTI platform
 * @route   POST /api/lti/platforms
 * @access  Private (Admin)
 */
export const registerPlatform = asyncHandler(async (req, res) => {
  try {
    const result = await registerPlatformData(req.body || {});
    await buildSuccessResponse(
      req,
      res,
      result,
      'Register Platform',
      'registerPlatform',
      'Register LTI Platform',
      httpStatus.CREATED
    );
  } catch (error) {
    logger.error('Platform registration failed:', error);
    throw error;
  }
});

/**
 * @desc    Get all registered platforms
 * @route   GET /api/lti/platforms
 * @access  Private (Admin)
 */
export const getPlatforms = asyncHandler(async (req, res) => {
  const result = await fetchPlatformsData();
  await buildSuccessResponse(
    req,
    res,
    result,
    'Get Platforms',
    'getPlatforms',
    'List LTI Platforms',
    httpStatus.OK
  );
});

/**
 * @desc    Update platform configuration
 * @route   PUT /api/lti/platforms/:id
 * @access  Private (Admin)
 */
export const updatePlatform = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const payload = req.body || {};
  const result = await updatePlatformById(id, payload);
  await buildSuccessResponse(
    req,
    res,
    result,
    'Update Platform',
    'updatePlatform',
    'Update LTI Platform',
    httpStatus.OK
  );
});

/**
 * @desc    Delete platform
 * @route   DELETE /api/lti/platforms/:id
 * @access  Private (Admin)
 */
export const deletePlatform = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const result = await deletePlatformById(id);
  await buildSuccessResponse(
    req,
    res,
    result,
    'Delete Platform',
    'deletePlatform',
    'Delete LTI Platform',
    httpStatus.OK
  );
});

/**
 * @desc    Send grade to LMS
 * @route   POST /api/lti/grades
 * @access  Private
 */
export const sendGradeToLMS = asyncHandler(async (req, res) => {
  const { submissionId, gradeId } = req.body || {};
  const result = await sendGradeToLMSUsingIds({ submissionId, gradeId });
  await buildSuccessResponse(
    req,
    res,
    result,
    'Send Grade to LMS',
    'sendGradeToLMS',
    'Send Grade via AGS',
    httpStatus.OK
  );
});

/**
 * @desc    Get LTI contexts for a course
 * @route   GET /api/lti/contexts/course/:courseId
 * @access  Private
 */
export const getCourseContexts = asyncHandler(async (req, res) => {
  const { courseId } = req.params;
  const result = await fetchCourseContextsData(courseId);
  await buildSuccessResponse(
    req,
    res,
    result,
    'Get Course Contexts',
    'getCourseContexts',
    'List LTI Contexts for Course',
    httpStatus.OK
  );
});

/**
 * Helper function to determine launch target based on LTI data
 */
async function determineLaunchTarget(
  launchData,
  resourceLink,
  courseId = null,
  resourceLinkId = null
) {
  const messageType =
    launchData['https://purl.imsglobal.org/spec/lti/claim/message_type'];

  logger.info(`determineLaunchTarget messageType: ${messageType}`);

  // Deep linking request
  if (messageType === 'LtiDeepLinkingRequest') {
    return 'deep-linking';
  }

  const roleIsLearner = launchData[
    'https://purl.imsglobal.org/spec/lti/claim/roles'
  ]?.some(role => role.includes('Learner'));

  // Resource link launch
  if (messageType === 'LtiResourceLinkRequest' && resourceLink?.projectId) {
    // Check if project exists and its status for learners
    if (roleIsLearner) {
      const project = await Project.findByPk(resourceLink.projectId);
      if (!project || project.status !== 'published') {
        const message = encodeURIComponent('Project not published yet');
        return `page-not-found?message=${message}`;
      }
    }

    const redirectPath = roleIsLearner
      ? `project-details/${resourceLink.projectId}?`
      : `view-project-details/${resourceLink.projectId}`;
    return redirectPath;
  }

  if (!roleIsLearner) {
    // Instructor/Admin: add courseId and resourceLinkId as query params to manage-projects
    let query = '';
    if (courseId || resourceLinkId) {
      const params = [];
      if (courseId) params.push(`courseId=${encodeURIComponent(courseId)}`);
      if (resourceLinkId)
        params.push(`resourceLinkId=${encodeURIComponent(resourceLinkId)}`);
      query = params.length ? `?${params.join('&')}` : '';
    }
    return `app/manage-projects${query}`;
  }
  const message = encodeURIComponent('404 - Not Found');
  // Learner fallback
  return `page-not-found?message=${message}`;
}

/**
 * @desc    Get LTI configuration for platform registration
 * @route   GET /api/lti/config
 * @access  Public
 */
export const getLTIConfiguration = asyncHandler(async (req, res) => {
  const toolUrl =
    process.env.LTI_TOOL_URL || req.protocol + '://' + req.get('host');
  const configuration = getLTIConfigurationData(toolUrl);
  res.json(configuration);
});

/**
 * @desc    Get LTI token cache statistics
 * @route   GET /api/lti/cache/stats
 * @access  Private (Admin)
 */
export const getTokenCacheStats = asyncHandler(async (req, res) => {
  try {
    const stats = await ltiService.getTokenCacheStats();
    await buildSuccessResponse(
      req,
      res,
      { success: true, stats },
      'Token Cache Stats',
      'getTokenCacheStats',
      'Fetch LTI Token Cache Stats',
      httpStatus.OK
    );
  } catch (error) {
    logger.error('[LTI] Failed to get token cache stats:', error);
    res.status(500).json({
      error: 'Cache Stats Failed',
      message: 'Could not retrieve token cache statistics'
    });
  }
});

/**
 * @desc    Invalidate cached tokens for a platform
 * @route   DELETE /api/lti/cache/platform/:platformId
 * @access  Private (Admin)
 */
export const invalidatePlatformTokens = asyncHandler(async (req, res) => {
  const { platformId } = req.params;
  const { scopes } = req.body;

  try {
    await ltiService.invalidateCachedTokens(platformId, scopes);
    const result = {
      success: true,
      message: scopes
        ? `Invalidated tokens for platform ${platformId} with scopes: ${scopes.join(', ')}`
        : `Invalidated all tokens for platform ${platformId}`
    };
    await buildSuccessResponse(
      req,
      res,
      result,
      'Invalidate Platform Tokens',
      'invalidatePlatformTokens',
      'Invalidate Cached LTI Tokens',
      httpStatus.OK
    );
  } catch (error) {
    logger.error('[LTI] Failed to invalidate platform tokens:', error);
    res.status(500).json({
      error: 'Token Invalidation Failed',
      message: 'Could not invalidate cached tokens'
    });
  }
});

/**
 * @desc    Clean up expired tokens
 * @route   POST /api/lti/cache/cleanup
 * @access  Private (Admin)
 */
export const cleanupExpiredTokens = asyncHandler(async (req, res) => {
  try {
    const cleanedCount = await ltiService.cleanupExpiredTokens();
    const result = {
      success: true,
      message: `Cleaned up ${cleanedCount} expired tokens`,
      cleanedCount
    };
    await buildSuccessResponse(
      req,
      res,
      result,
      'Cleanup Expired Tokens',
      'cleanupExpiredTokens',
      'Cleanup LTI Tokens',
      httpStatus.OK
    );
  } catch (error) {
    logger.error('[LTI] Failed to cleanup expired tokens:', error);
    res.status(500).json({
      error: 'Token Cleanup Failed',
      message: 'Could not cleanup expired tokens'
    });
  }
});

/**
 * @desc    Fetch Brightspace roster via NRPS using launch claims
 * @route   GET /api/lti/nrps/memberships
 * @access  Private (requires prior LTI launch session)
 */
export const getRoster = asyncHandler(async (req, res) => {
  const launchData = req.session?.ltiLaunchData;
  const context = req.session?.ltiContext;
  const result = await parseRosterMembers(launchData, context, req.query);
  await buildSuccessResponse(
    req,
    res,
    result,
    'Get NRPS Roster',
    'getRoster',
    'Fetch NRPS Memberships',
    httpStatus.OK
  );
});

/**
 * @desc    Fetch Brightspace gradebook columns (line items) via AGS
 * @route   GET /api/lti/ags/lineitems
 * @access  Private (requires prior LTI launch session)
 */
export const getLineItems = asyncHandler(async (req, res) => {
  logger.info(
    '[LTI AGS] getLineItems called with query:',
    JSON.stringify(req.session, null, 2)
  );
  const launchData = req.session?.ltiLaunchData;
  const context = req.session?.ltiContext;
  const result = await parseLineItemsFromLaunch(launchData, context, req.query);
  await buildSuccessResponse(
    req,
    res,
    result,
    'Get AGS Line Items',
    'getLineItems',
    'Fetch AGS Line Items',
    httpStatus.OK
  );
});

/**
 * @desc    Fetch a single Brightspace line item by resourceLinkId via AGS
 * @route   GET /api/lti/ags/lineitems/by-resource-link-id?resourceLinkId=...
 * @access  Private (requires prior LTI launch session)
 */
export const getLineItemByResourceLinkId = asyncHandler(
  async (req, res) => {
    // Local logging helper to force inline msg + details for server output
    component = 'getLineItemByResourceLinkId';
    auditComponent = 'Get Line Item by Resource Link ID';
    const { resourceLinkId } = req.params;
    const launchData = req.session?.ltiLaunchData;
    const context = req.session?.ltiContext;

    const result = await parseLineItemByResourceLinkId(
      launchData,
      context,
      resourceLinkId
    );

    await buildSuccessResponse(
      req,
      res,
      result,
      'Get Line Item by Resource Link ID',
      component,
      auditComponent,
      httpStatus.OK
    );
  },
  { component, auditComponent }
);

/**
 * @desc Get only students from NRPS memberships
 */
export const getStudents = asyncHandler(async (req, res) => {
  const launchData = req.session?.ltiLaunchData;
  const context = req.session?.ltiContext;
  const result = await parseStudentsFromNRPS(launchData, context);
  await buildSuccessResponse(
    req,
    res,
    result,
    'Get Students',
    'getStudents',
    'Fetch Students from NRPS',
    httpStatus.OK
  );
});

/**
 * @desc Get only instructors from NRPS memberships
 */
export const getInstructors = asyncHandler(async (req, res) => {
  const launchData = req.session?.ltiLaunchData;
  const context = req.session?.ltiContext;
  const result = await parseInstructorsFromNRPS(launchData, context);
  await buildSuccessResponse(
    req,
    res,
    result,
    'Get Instructors',
    'getInstructors',
    'Fetch Instructors from NRPS',
    httpStatus.OK
  );
});

/**
 * @desc Create a line item (local + remote) manually
 */
export const createLineItem = asyncHandler(async (req, res) => {
  const launchData = req.session?.ltiLaunchData;
  const context = req.session?.ltiContext;
  const resourceLink = req.session?.ltiResourceLink;
  const result = await createLineItemFromSession(
    launchData,
    context,
    resourceLink,
    req.body || {}
  );
  await buildSuccessResponse(
    req,
    res,
    result,
    'Create Line Item',
    'createLineItem',
    'Create Line Item (Local + Remote)',
    httpStatus.OK
  );
});

/**
 * @desc Post a score (grade passback) using submission id
 */
export const postScore = asyncHandler(async (req, res) => {
  const launchData = req.session?.ltiLaunchData;
  const context = req.session?.ltiContext;
  const resourceLink = req.session?.ltiResourceLink;
  const result = await postScoreFromSubmission(
    launchData,
    context,
    resourceLink,
    req.body || {}
  );
  await buildSuccessResponse(
    req,
    res,
    result,
    'Post Score',
    'postScore',
    'Post Score to AGS',
    httpStatus.OK
  );
});

/**
 * @desc List all results for a line item (by projectId or lineItemId)
 */
export const listResults = asyncHandler(async (req, res) => {
  const context = req.session?.ltiContext;
  const result = await listResultsForLineItem(context, req.query || {});
  await buildSuccessResponse(
    req,
    res,
    result,
    'List Results',
    'listResults',
    'List Results for Line Item',
    httpStatus.OK
  );
});

/**
 * @desc Get current user result for line item
 */
export const getMyResult = asyncHandler(async (req, res) => {
  const user = req.session?.user;
  const context = req.session?.ltiContext;
  const result = await getMyResultForLineItem(context, user, req.query || {});
  await buildSuccessResponse(
    req,
    res,
    result,
    'Get My Result',
    'getMyResult',
    'Get Current User Result',
    httpStatus.OK
  );
});

/**
 * @desc Service/session status summary
 */
export const getLTIServiceStatus = asyncHandler(async (req, res) => {
  const result = await getLTIServiceStatusSummary(req.session);
  await buildSuccessResponse(
    req,
    res,
    result,
    'LTI Service Status',
    'getLTIServiceStatus',
    'LTI Service/Session Summary',
    httpStatus.OK
  );
});

/**
 * @desc    Link local project to LMS resource link/line item and update LMS resourceId
 * @route   POST /api/lti/ags/link-project
 * @access  Private
 */
export const linkProjectToLmsLineItem = asyncHandler(async (req, res) => {
  const { resourceLinkId, projectId, instructor_ids, teaching_ass_ids } =
    req.body || {};
  const result = await linkProjectWithLmsLineItem({
    resourceLinkId,
    projectId,
    instructor_ids,
    teaching_ass_ids,
    session: req.session
  });
  await buildSuccessResponse(
    req,
    res,
    result,
    'Link Project & Line Item',
    'linkProjectToLmsLineItem',
    'Link Project & Update Line Item ResourceId',
    httpStatus.OK
  );
});

/**
 * @desc    Get a paginated list of resource links not associated with any project.
 * @route   GET /api/lti/resource-links/unassigned
 * @access  Private (Instructor/Admin)
 * @returns {object} Paginated list of resource links.
 */
export const getUnassignedResourceLinks = asyncHandler(async (req, res) => {
  const { contextId, platformId, page, limit, sortBy } = req.query;
  const result = await getResourceLinksWithoutProject({
    contextId,
    platformId,
    page,
    limit,
    sortBy
  });
  await buildSuccessResponse(
    req,
    res,
    result,
    'Get Unassigned Resource Links',
    'getUnassignedResourceLinks',
    'Fetch unassigned LTI resource links',
    httpStatus.OK
  );
});

/**
 * @desc    Get all LTI contexts with optional pagination
 * @route   GET /api/lti/contexts
 * @access  Private
 */
export const getAllContexts = asyncHandler(async (req, res) => {
  const { page, limit } = req.query || {};
  // Basic validation: positive integers if provided
  const pageNum = page != null ? parseInt(page, 10) : undefined;
  const limitNum = limit != null ? parseInt(limit, 10) : undefined;
  if (
    (page != null && Number.isNaN(pageNum)) ||
    (limit != null && Number.isNaN(limitNum))
  ) {
    return res
      .status(httpStatus.BAD_REQUEST)
      .json({ success: false, message: 'Invalid pagination parameters' });
  }

  const result = await fetchAllContextsData({ page: pageNum, limit: limitNum });
  await buildSuccessResponse(
    req,
    res,
    result,
    'Get All Contexts',
    'getAllContexts',
    'List all LTI Contexts',
    httpStatus.OK
  );
});

/**
 * @desc    Get users for a context filtered by platform and optional role
 * @route   GET /api/lti/contexts/members
 * @access  Private
 */
export const getContextMembers = asyncHandler(async (req, res) => {
  const { contextId, platformId, role } = req.query || {};
  if (!contextId || !platformId) {
    return res.status(httpStatus.BAD_REQUEST).json({
      success: false,
      message: 'contextId and platformId are required'
    });
  }

  const result = await fetchContextMembersData({
    contextId,
    platformId,
    role
  });
  await buildSuccessResponse(
    req,
    res,
    result,
    'Get Context Instructors',
    'getContextInstructors',
    'List users for context by role',
    httpStatus.OK
  );
});
