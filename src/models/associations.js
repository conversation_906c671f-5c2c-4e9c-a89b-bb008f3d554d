import User from './User.models.js';
import Role from './Role.models.js';
import Permission from './Permission.models.js';
import UserRole from './UserRole.models.js';
import RolePermission from './RolePermission.models.js';
import Project from './Project.models.js';
import ProjectSandboxSettings from './ProjectSandboxSettings.models.js';
import UserWorkspace from './UserWorkspace.models.js';
import Submission from './Submission.models.js';
import Grade from './Grade.models.js';
import Rubric from './Rubric.models.js';
import Checkpoint from './Checkpoint.models.js';
import CheckpointGoal from './CheckpointGoal.models.js';
import CheckpointProgress from './CheckpointProgress.models.js';
import CheckpointGrade from './CheckpointGrade.models.js';
import Activity from './Activity.models.js';
import ProjectStatistics from './ProjectStatistics.models.js';
import Announcement from './Announcement.models.js';
import Message from './Message.models.js';
import ProjectTemplate from './ProjectTemplate.models.js';
import ProjectAssignment from './ProjectAssignment.models.js';
import StudentProjectProgress from './StudentProjectProgress.models.js';
import StudentActivity from './StudentActivity.models.js';
import LtiContextEnrollment from './LtiContextEnrollment.models.js';
import {
  LtiPlatform,
  LtiDeployment,
  LtiContext,
  LtiResourceLink,
  LtiLineItem,
  LtiLaunchSession
} from './ltiAssociations.models.js';

// User associations
User.belongsToMany(Role, {
  through: UserRole,
  foreignKey: 'user_id',
  otherKey: 'role_id',
  as: 'roles'
});

Role.belongsToMany(User, {
  through: UserRole,
  foreignKey: 'role_id',
  otherKey: 'user_id',
  as: 'users'
});

// Role associations
Role.belongsToMany(Permission, {
  through: RolePermission,
  foreignKey: 'role_id',
  otherKey: 'permission_id',
  as: 'permissions'
});

Permission.belongsToMany(Role, {
  through: RolePermission,
  foreignKey: 'permission_id',
  otherKey: 'role_id',
  as: 'roles'
});

// LtiContext associations - removed instructor_id associations as column doesn't exist

LtiContext.belongsToMany(User, {
  through: LtiContextEnrollment,
  foreignKey: 'context_id',
  otherKey: 'user_id',
  as: 'enrolledUsers'
});

User.belongsToMany(LtiContext, {
  through: LtiContextEnrollment,
  foreignKey: 'user_id',
  otherKey: 'context_id',
  as: 'enrolledContexts'
});

LtiContextEnrollment.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

User.hasMany(LtiContextEnrollment, {
  as: 'enrollments',
  foreignKey: 'user_id'
});

LtiContextEnrollment.belongsTo(LtiContext, {
  foreignKey: 'context_id',
  as: 'course'
});

LtiContext.hasMany(LtiContextEnrollment, {
  foreignKey: 'context_id',
  as: 'enrollments'
});

// Project associations
Project.belongsTo(LtiContext, {
  foreignKey: 'course_id',
  as: 'course'
});

LtiContext.hasMany(Project, {
  foreignKey: 'course_id',
  as: 'projects'
});

Project.belongsTo(User, {
  foreignKey: 'created_by',
  as: 'creator'
});

User.hasMany(Project, {
  foreignKey: 'created_by',
  as: 'createdProjects'
});

// ProjectSandboxSettings associations
ProjectSandboxSettings.belongsTo(Project, {
  foreignKey: 'projectId',
  as: 'project'
});

Project.hasMany(ProjectSandboxSettings, {
  foreignKey: 'projectId',
  as: 'sandboxSettings'
});

ProjectSandboxSettings.belongsTo(User, {
  foreignKey: 'createdBy',
  as: 'creator'
});

ProjectSandboxSettings.belongsTo(User, {
  foreignKey: 'updatedBy',
  as: 'updater'
});

// UserWorkspace associations
UserWorkspace.belongsTo(User, {
  foreignKey: 'studentId',
  as: 'student'
});

UserWorkspace.belongsTo(Project, {
  foreignKey: 'projectId',
  as: 'project'
});

User.hasMany(UserWorkspace, {
  foreignKey: 'studentId',
  as: 'workspaces'
});

Project.hasMany(UserWorkspace, {
  foreignKey: 'projectId',
  as: 'userWorkspaces'
});

// Submission associations
Submission.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

User.hasMany(Submission, {
  foreignKey: 'user_id',
  as: 'submissions'
});

Submission.belongsTo(Project, {
  foreignKey: 'project_id',
  as: 'project'
});

Project.hasMany(Submission, {
  foreignKey: 'project_id',
  as: 'submissions'
});

// Grade associations
Grade.belongsTo(Submission, {
  foreignKey: 'submission_id',
  as: 'submission'
});

Submission.hasOne(Grade, {
  foreignKey: 'submission_id',
  as: 'grade'
});

Grade.belongsTo(User, {
  foreignKey: 'evaluator_id',
  as: 'evaluator'
});

User.hasMany(Grade, {
  foreignKey: 'evaluator_id',
  as: 'gradedSubmissions'
});

// Rubric associations
Rubric.belongsTo(Project, {
  foreignKey: 'project_id',
  as: 'project'
});

Project.hasMany(Rubric, {
  foreignKey: 'project_id',
  as: 'rubrics'
});

Rubric.belongsTo(Checkpoint, {
  foreignKey: 'checkpoint_id',
  as: 'checkpoint'
});

Checkpoint.hasMany(Rubric, {
  foreignKey: 'checkpoint_id',
  as: 'rubrics'
});

Rubric.belongsTo(User, {
  foreignKey: 'created_by',
  as: 'creator'
});

User.hasMany(Rubric, {
  foreignKey: 'created_by',
  as: 'createdRubrics'
});

// Checkpoint associations
Checkpoint.belongsTo(Project, {
  foreignKey: 'project_id',
  as: 'project'
});

Project.hasMany(Checkpoint, {
  foreignKey: 'project_id',
  as: 'checkpoints'
});

Checkpoint.belongsTo(User, {
  foreignKey: 'created_by',
  as: 'creator'
});

User.hasMany(Checkpoint, {
  foreignKey: 'created_by',
  as: 'createdCheckpoints'
});

// CheckpointGoal associations
CheckpointGoal.belongsTo(Checkpoint, {
  foreignKey: 'checkpoint_id',
  as: 'checkpoint'
});

Checkpoint.hasMany(CheckpointGoal, {
  foreignKey: 'checkpoint_id',
  as: 'goals'
});

// CheckpointProgress associations
CheckpointProgress.belongsTo(Checkpoint, {
  foreignKey: 'checkpoint_id',
  as: 'checkpoint'
});

Checkpoint.hasMany(CheckpointProgress, {
  foreignKey: 'checkpoint_id',
  as: 'progress'
});

CheckpointProgress.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

User.hasMany(CheckpointProgress, {
  foreignKey: 'user_id',
  as: 'checkpointProgress'
});

CheckpointProgress.belongsTo(Project, {
  foreignKey: 'project_id',
  as: 'project'
});

Project.hasMany(CheckpointProgress, {
  foreignKey: 'project_id',
  as: 'checkpointProgress'
});

// CheckpointGrade associations
CheckpointGrade.belongsTo(CheckpointProgress, {
  foreignKey: 'checkpoint_progress_id',
  as: 'checkpointProgress'
});

CheckpointProgress.hasOne(CheckpointGrade, {
  foreignKey: 'checkpoint_progress_id',
  as: 'grade'
});

CheckpointGrade.belongsTo(User, {
  foreignKey: 'evaluator_id',
  as: 'evaluator'
});

User.hasMany(CheckpointGrade, {
  foreignKey: 'evaluator_id',
  as: 'checkpointGrades'
});

// Activity associations
Activity.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user'
});

User.hasMany(Activity, {
  foreignKey: 'user_id',
  as: 'activities'
});

Activity.belongsTo(Project, {
  foreignKey: 'project_id',
  as: 'project'
});

Project.hasMany(Activity, {
  foreignKey: 'project_id',
  as: 'activities'
});

Activity.belongsTo(LtiContext, {
  foreignKey: 'course_id',
  as: 'course'
});

LtiContext.hasMany(Activity, {
  foreignKey: 'course_id',
  as: 'contextActivities'
});

// ProjectStatistics associations
ProjectStatistics.belongsTo(Project, {
  foreignKey: 'project_id',
  as: 'project'
});

Project.hasOne(ProjectStatistics, {
  foreignKey: 'project_id',
  as: 'statistics'
});

// ProjectTemplate associations
Project.hasOne(ProjectTemplate, {
  foreignKey: 'project_id',
  as: 'template'
});

ProjectTemplate.belongsTo(Project, {
  foreignKey: 'project_id',
  as: 'project'
});

ProjectTemplate.belongsTo(User, {
  foreignKey: 'created_by',
  as: 'createdBy'
});

User.hasMany(ProjectTemplate, {
  foreignKey: 'created_by',
  as: 'createdTemplates'
});

ProjectTemplate.belongsTo(User, {
  foreignKey: 'reviewed_by',
  as: 'reviewedBy'
});

User.hasMany(ProjectTemplate, {
  foreignKey: 'reviewed_by',
  as: 'reviewedTemplates'
});

ProjectTemplate.belongsTo(User, {
  foreignKey: 'deleted_by',
  as: 'deletedBy'
});

User.hasMany(ProjectTemplate, {
  foreignKey: 'deleted_by',
  as: 'deletedTemplates'
});

// ProjectAssignment associations
Project.hasMany(ProjectAssignment, {
  foreignKey: 'project_id',
  as: 'assignments'
});

ProjectAssignment.belongsTo(Project, {
  foreignKey: 'project_id',
  as: 'project'
});

ProjectAssignment.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'assignedUser'
});

User.hasMany(ProjectAssignment, {
  foreignKey: 'user_id',
  as: 'projectAssignments'
});

// Announcement associations
Announcement.belongsTo(LtiContext, {
  foreignKey: 'course_id',
  as: 'course'
});

LtiContext.hasMany(Announcement, {
  foreignKey: 'course_id',
  as: 'announcements'
});

Announcement.belongsTo(User, {
  foreignKey: 'created_by',
  as: 'creator'
});

User.hasMany(Announcement, {
  foreignKey: 'created_by',
  as: 'createdAnnouncements'
});

Announcement.belongsTo(User, {
  foreignKey: 'published_by',
  as: 'publisher'
});

User.hasMany(Announcement, {
  foreignKey: 'published_by',
  as: 'publishedAnnouncements'
});

// Message associations
Message.belongsTo(User, {
  foreignKey: 'sender_id',
  as: 'sender'
});

User.hasMany(Message, {
  foreignKey: 'sender_id',
  as: 'sentMessages'
});

Message.belongsTo(User, {
  foreignKey: 'recipient_id',
  as: 'recipient'
});

User.hasMany(Message, {
  foreignKey: 'recipient_id',
  as: 'receivedMessages'
});

Message.belongsTo(Message, {
  foreignKey: 'parent_message_id',
  as: 'parentMessage'
});

Message.hasMany(Message, {
  foreignKey: 'parent_message_id',
  as: 'replies'
});

Message.belongsTo(LtiContext, {
  foreignKey: 'course_id',
  as: 'course'
});

LtiContext.hasMany(Message, {
  foreignKey: 'course_id',
  as: 'contextMessages'
});

Message.belongsTo(Project, {
  foreignKey: 'project_id',
  as: 'project'
});

Project.hasMany(Message, {
  foreignKey: 'project_id',
  as: 'projectMessages'
});

// Student Project Progress associations
StudentProjectProgress.belongsTo(User, {
  foreignKey: 'student_id',
  as: 'student'
});

User.hasMany(StudentProjectProgress, {
  foreignKey: 'student_id',
  as: 'projectProgress'
});

StudentProjectProgress.belongsTo(Project, {
  foreignKey: 'project_id',
  as: 'project'
});

Project.hasMany(StudentProjectProgress, {
  foreignKey: 'project_id',
  as: 'studentProgress'
});

StudentProjectProgress.belongsTo(LtiContext, {
  foreignKey: 'course_id',
  as: 'course'
});

LtiContext.hasMany(StudentProjectProgress, {
  foreignKey: 'course_id',
  as: 'studentProjectProgress'
});

// Student Activity associations
StudentActivity.belongsTo(User, {
  foreignKey: 'student_id',
  as: 'student'
});

User.hasMany(StudentActivity, {
  foreignKey: 'student_id',
  as: 'studentActivities'
});

StudentActivity.belongsTo(Project, {
  foreignKey: 'project_id',
  as: 'project'
});

Project.hasMany(StudentActivity, {
  foreignKey: 'project_id',
  as: 'studentActivities'
});

StudentActivity.belongsTo(LtiContext, {
  foreignKey: 'course_id',
  as: 'course'
});

/* LtiContext.hasMany(StudentActivity, {
  foreignKey: 'course_id',
  as: 'contextActivities'
}); */



// Export all models for easy importing
export {
  User,
  Role,
  Permission,
  UserRole,
  RolePermission,
  Project,
  ProjectSandboxSettings,
  UserWorkspace,
  Submission,
  Grade,
  Rubric,
  Checkpoint,
  CheckpointGoal,
  CheckpointProgress,
  CheckpointGrade,
  Activity,
  ProjectStatistics,
  Announcement,
  Message,
  ProjectTemplate,
  ProjectAssignment,
  StudentProjectProgress,
  StudentActivity,
  LtiContextEnrollment,
  // LTI Models
  LtiPlatform,
  LtiDeployment,
  LtiContext,
  LtiResourceLink,
  LtiLineItem,
  LtiLaunchSession
};
