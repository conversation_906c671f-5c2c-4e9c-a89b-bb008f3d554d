import User from './User.models.js';
import Project from './Project.models.js';
import LtiPlatform from './LtiPlatform.models.js';
import LtiDeployment from './LtiDeployment.models.js';
import LtiContext from './LtiContext.models.js';
import LtiResourceLink from './LtiResourceLink.models.js';
import LtiLineItem from './LtiLineItem.models.js';
import LtiLaunchSession from './LtiLaunchSession.models.js';

// LTI Platform associations
LtiPlatform.hasMany(LtiDeployment, {
  foreignKey: 'platformId',
  as: 'deployments'
});
LtiDeployment.belongsTo(LtiPlatform, {
  foreignKey: 'platformId',
  as: 'platform'
});

LtiPlatform.hasMany(LtiContext, {
  foreignKey: 'platformId',
  as: 'contexts'
});
LtiContext.belongsTo(LtiPlatform, {
  foreignKey: 'platformId',
  as: 'platform'
});

LtiPlatform.hasMany(LtiResourceLink, {
  foreignKey: 'platformId',
  as: 'resourceLinks'
});
LtiResourceLink.belongsTo(LtiPlatform, {
  foreignKey: 'platformId',
  as: 'platform'
});

LtiPlatform.hasMany(LtiLineItem, {
  foreignKey: 'platformId',
  as: 'lineItems'
});
LtiLineItem.belongsTo(LtiPlatform, {
  foreignKey: 'platformId',
  as: 'platform'
});

LtiPlatform.hasMany(LtiLaunchSession, {
  foreignKey: 'platformId',
  as: 'launchSessions'
});
LtiLaunchSession.belongsTo(LtiPlatform, {
  foreignKey: 'platformId',
  as: 'platform'
});

// LTI Deployment associations
LtiDeployment.hasMany(LtiContext, {
  foreignKey: 'deploymentId',
  as: 'contexts'
});
LtiContext.belongsTo(LtiDeployment, {
  foreignKey: 'deploymentId',
  as: 'deployment'
});

/* // LTI Context associations
LtiContext.belongsTo(Course, {
  foreignKey: 'courseId',
  as: 'course'
});
Course.hasMany(LtiContext, {
  foreignKey: 'courseId',
  as: 'ltiContexts'
}); */

LtiContext.hasMany(LtiResourceLink, {
  foreignKey: 'contextId',
  as: 'resourceLinks'
});
LtiResourceLink.belongsTo(LtiContext, {
  foreignKey: 'contextId',
  as: 'context'
});

LtiContext.hasMany(LtiLineItem, {
  foreignKey: 'contextId',
  as: 'lineItems'
});
LtiLineItem.belongsTo(LtiContext, {
  foreignKey: 'contextId',
  as: 'context'
});

// LTI Resource Link associations
LtiResourceLink.belongsTo(Project, {
  foreignKey: 'projectId',
  as: 'project'
});
Project.hasMany(LtiResourceLink, {
  foreignKey: 'projectId',
  as: 'ltiResourceLinks'
});

LtiResourceLink.hasMany(LtiLineItem, {
  foreignKey: 'resourceLinkId',
  as: 'lineItems'
});
LtiLineItem.belongsTo(LtiResourceLink, {
  foreignKey: 'resourceLinkId',
  as: 'resourceLink'
});

// LTI Line Item associations
LtiLineItem.belongsTo(Project, {
  foreignKey: 'projectId',
  as: 'project'
});
Project.hasMany(LtiLineItem, {
  foreignKey: 'projectId',
  as: 'ltiLineItems'
});

// LTI Launch Session associations
LtiLaunchSession.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
});
User.hasMany(LtiLaunchSession, {
  foreignKey: 'userId',
  as: 'ltiLaunchSessions'
});

// Export all LTI models for easy import
export {
  LtiPlatform,
  LtiDeployment,
  LtiContext,
  LtiResourceLink,
  LtiLineItem,
  LtiLaunchSession
};