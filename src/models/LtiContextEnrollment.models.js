import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.config.js';

const LtiContextEnrollment = sequelize.define(
  'LtiContextEnrollment',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    context_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'lti_contexts',
        key: 'id'
      }
    },
    role_in_course: {
      // Keep same enum name as CourseEnrollment for parity
      type: DataTypes.ENUM('student', 'instructor', 'ta', 'observer'),
      allowNull: false,
      defaultValue: 'student'
    },
    enrollment_status: {
      type: DataTypes.ENUM('active', 'dropped', 'completed', 'withdrawn'),
      defaultValue: 'active'
    },
    enrolled_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    dropped_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    final_grade: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: true,
      comment: 'Final context grade (0-100)'
    },
    metadata: {
      type: DataTypes.JSONB,
      allowNull: true,
      defaultValue: {},
      comment: 'Additional enrollment metadata from LMS (NRPS)'
    }
  },
  {
    tableName: 'lti_context_enrollments',
    indexes: [
      {
        unique: true,
        fields: ['user_id', 'context_id']
      },
      { fields: ['user_id'] },
      { fields: ['context_id'] },
      { fields: ['role_in_course'] },
      { fields: ['enrollment_status'] }
    ]
  }
);

export default LtiContextEnrollment;
