# Sequelize Migration Guide

This guide covers how to use the Sequelize migration system in your project.

## Migration Commands

### Basic Migration Management
- `npm run db:migrate` - Run all pending migrations
- `npm run db:migrate:undo` - Undo the last migration
- `npm run db:migrate:undo:all` - Undo all migrations
- `npm run db:migrate:status` - Check migration status

### Creating Migrations
- `npm run db:migrate:create <name>` - Create a new migration file
- `npm run migration:create <name>` - Alternative method with helper

### Migration Control
- `npm run db:migrate:up <migration-name>` - Migrate to a specific migration
- `npm run db:migrate:down <migration-name>` - Rollback to a specific migration

### Database Management
- `npm run db:create` - Create the database
- `npm run db:drop` - Drop the database
- `npm run db:reset` - Complete database reset
- `npm run db:schema:drop` - Drop and recreate database schema

### Seeding
- `npm run db:seed` - Run all seeders
- `npm run db:seed:undo` - Undo all seeders
- `npm run db:seed:create <name>` - Create a new seeder

## Migration Workflow

### When you need to create a migration:
1. Make changes to your Sequelize models
2. Create a migration: `npm run db:migrate:create <descriptive-name>`
3. Edit the migration file with your schema changes
4. Run the migration: `npm run db:migrate`
5. Verify: `npm run db:migrate:status`

### Example Migration Structure
```javascript
export async function up(queryInterface) {
  // Add your migration logic here
  await queryInterface.createTable('new_table', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    // Add more columns as needed
  });
}

export async function down(queryInterface) {
  // Add your rollback logic here
  await queryInterface.dropTable('new_table');
}
```

## Migration Helper

Use the migration helper for better workflow management:
- `npm run migration:help` - Show all available commands
- `npm run migration:status` - Check migration status
- `npm run migration:check` - Comprehensive migration check

## Best Practices

✅ **Always do:**
- Create migrations for any schema changes
- Write both `up()` and `down()` methods
- Test migrations in development first
- Use descriptive migration names
- Keep migrations atomic and reversible

❌ **Never do:**
- Edit existing migrations after deployment
- Run migrations directly in production without testing
- Forget to test the `down()` method
- Make breaking changes without proper migration

## Environment Variables

Make sure your `.env` file has these database settings:
```
DB_USERNAME=your_username
DB_PASSWORD=your_password
DB_NAME=your_database_name
DB_HOST=localhost
DB_PORT=5432
DATABASE_URL=postgresql://username:password@host:port/database
NODE_ENV=development
```

## Troubleshooting

### Common Issues:
1. **Migration not found**: Check if the migration file exists in `src/migrations/`
2. **Database connection issues**: Verify your database credentials and connection
3. **Migration stuck**: Check for locked tables or incomplete transactions
4. **Rollback issues**: Ensure your `down()` method is properly implemented

### Getting Help:
Run `npm run migration:help` for comprehensive command reference and troubleshooting tips.