{"info": {"_postman_id": "checkpoint-api-collection", "name": "BITS DataScience - Checkpoint APIs", "description": "Complete collection of checkpoint management APIs for BITS DataScience Platform", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Create Checkpoints", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "[\n  {\n    \"project_id\": \"{{project_id}}\",\n    \"title\": \"Data Collection & Preprocessing\",\n    \"description\": \"Collect and clean the dataset for analysis\",\n    \"checkpoint_number\": 1,\n    \"start_date\": \"2024-01-15T00:00:00.000Z\",\n    \"due_date\": \"2024-01-30T23:59:59.000Z\",\n    \"weight_percentage\": 25,\n    \"is_required\": true,\n    \"status\": \"published\",\n    \"isScreen\": 3,\n    \"metadata\": {\n      \"difficulty\": \"medium\",\n      \"estimated_hours\": 10\n    }\n  },\n  {\n    \"project_id\": \"{{project_id}}\",\n    \"title\": \"Exploratory Data Analysis\",\n    \"description\": \"Perform EDA and generate insights\",\n    \"checkpoint_number\": 2,\n    \"start_date\": \"2024-02-01T00:00:00.000Z\",\n    \"due_date\": \"2024-02-15T23:59:59.000Z\",\n    \"weight_percentage\": 30,\n    \"is_required\": true,\n    \"status\": \"draft\",\n    \"isScreen\": 2\n  }\n]"}, "url": {"raw": "{{base_url}}/api/checkpoints", "host": ["{{base_url}}"], "path": ["api", "checkpoints"]}}}, {"name": "Bulk Update Checkpoints", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"project_id\": \"{{project_id}}\",\n  \"checkpoints\": [\n    {\n      \"id\": \"{{checkpoint_id_1}}\",\n      \"title\": \"Updated Data Collection\",\n      \"description\": \"Enhanced data collection with validation\",\n      \"checkpoint_number\": 1,\n      \"due_date\": \"2024-02-05T23:59:59.000Z\",\n      \"weight_percentage\": 30,\n      \"is_required\": true,\n      \"status\": \"published\"\n    },\n    {\n      \"title\": \"New Model Training Checkpoint\",\n      \"description\": \"Train and validate ML models\",\n      \"checkpoint_number\": 3,\n      \"due_date\": \"2024-03-01T23:59:59.000Z\",\n      \"weight_percentage\": 35,\n      \"is_required\": true,\n      \"status\": \"draft\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/checkpoints/bulk-update", "host": ["{{base_url}}"], "path": ["api", "checkpoints", "bulk-update"]}}}, {"name": "Update Project Checkpoints", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "[\n  {\n    \"id\": \"{{checkpoint_id}}\",\n    \"title\": \"Updated Checkpoint Title\",\n    \"description\": \"Updated description\",\n    \"checkpoint_number\": 1,\n    \"start_date\": \"2024-01-20T00:00:00.000Z\",\n    \"due_date\": \"2024-02-10T23:59:59.000Z\",\n    \"weight_percentage\": 35,\n    \"is_required\": true,\n    \"status\": \"published\"\n  }\n]"}, "url": {"raw": "{{base_url}}/api/checkpoints/project/{{project_id}}", "host": ["{{base_url}}"], "path": ["api", "checkpoints", "project", "{{project_id}}"]}}}, {"name": "Get Checkpoint Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/checkpoints/{{checkpoint_id}}", "host": ["{{base_url}}"], "path": ["api", "checkpoints", "{{checkpoint_id}}"]}}}, {"name": "Update Checkpoint Progress", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"projectId\": \"{{project_id}}\",\n  \"updates\": {\n    \"status\": \"in_progress\",\n    \"goal_progress\": {\n      \"data_collection\": 75,\n      \"data_cleaning\": 50\n    },\n    \"student_notes\": \"Making good progress on data collection\",\n    \"auto_save_data\": {\n      \"last_saved\": \"2024-01-25T10:30:00.000Z\",\n      \"current_step\": \"validation\"\n    },\n    \"time_spent_minutes\": 120\n  }\n}"}, "url": {"raw": "{{base_url}}/api/checkpoints/{{checkpoint_id}}/progress", "host": ["{{base_url}}"], "path": ["api", "checkpoints", "{{checkpoint_id}}", "progress"]}}}, {"name": "Submit Checkpoint", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"projectId\": \"{{project_id}}\",\n  \"files\": [\n    {\n      \"filename\": \"dataset_cleaned.csv\",\n      \"s3_url\": \"https://s3.amazonaws.com/bucket/dataset_cleaned.csv\",\n      \"file_type\": \"csv\",\n      \"size\": 2048576\n    },\n    {\n      \"filename\": \"preprocessing_notebook.ipynb\",\n      \"s3_url\": \"https://s3.amazonaws.com/bucket/preprocessing.ipynb\",\n      \"file_type\": \"notebook\",\n      \"size\": 512000\n    }\n  ],\n  \"notes\": \"Completed data preprocessing with 95% data quality. Applied feature engineering techniques.\"\n}"}, "url": {"raw": "{{base_url}}/api/checkpoints/{{checkpoint_id}}/submit", "host": ["{{base_url}}"], "path": ["api", "checkpoints", "{{checkpoint_id}}", "submit"]}}}, {"name": "Grade Checkpoint", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"rubricScores\": {\n    \"data_quality\": 85,\n    \"methodology\": 90,\n    \"documentation\": 80,\n    \"code_quality\": 88\n  },\n  \"totalScore\": 86.5,\n  \"maxScore\": 100,\n  \"feedback\": \"Excellent work on data preprocessing. Good use of feature engineering techniques.\",\n  \"detailedFeedback\": {\n    \"strengths\": [\n      \"Clean and well-documented code\",\n      \"Appropriate handling of missing values\",\n      \"Good feature selection methodology\"\n    ],\n    \"improvements\": [\n      \"Could explore more advanced feature engineering\",\n      \"Add more statistical analysis in EDA\"\n    ]\n  }\n}"}, "url": {"raw": "{{base_url}}/api/checkpoints/{{checkpoint_progress_id}}/grade", "host": ["{{base_url}}"], "path": ["api", "checkpoints", "{{checkpoint_progress_id}}", "grade"]}}}, {"name": "Get Checkpoint Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/checkpoints/{{checkpoint_id}}/analytics", "host": ["{{base_url}}"], "path": ["api", "checkpoints", "{{checkpoint_id}}", "analytics"]}}}, {"name": "Delete Checkpoint", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/checkpoints/{{checkpoint_id}}", "host": ["{{base_url}}"], "path": ["api", "checkpoints", "{{checkpoint_id}}"]}}}, {"name": "Get Instructor Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/checkpoints/instructor/courses/{{course_id}}/dashboard", "host": ["{{base_url}}"], "path": ["api", "checkpoints", "instructor", "courses", "{{course_id}}", "dashboard"]}}}, {"name": "Get Student Progress Overview", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/checkpoints/instructor/courses/{{course_id}}/progress-overview?projectId={{project_id}}", "host": ["{{base_url}}"], "path": ["api", "checkpoints", "instructor", "courses", "{{course_id}}", "progress-overview"], "query": [{"key": "projectId", "value": "{{project_id}}"}]}}}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:5001", "type": "string"}, {"key": "jwt_token", "value": "your_jwt_token_here", "type": "string"}, {"key": "project_id", "value": "your_project_uuid_here", "type": "string"}, {"key": "checkpoint_id", "value": "your_checkpoint_uuid_here", "type": "string"}, {"key": "checkpoint_id_1", "value": "your_first_checkpoint_uuid_here", "type": "string"}, {"key": "checkpoint_progress_id", "value": "your_checkpoint_progress_uuid_here", "type": "string"}, {"key": "course_id", "value": "your_course_uuid_here", "type": "string"}]}