{"info": {"_postman_id": "rubric-api-collection-updated", "name": "BITS DataScience - Rubric APIs (Updated)", "description": "Updated collection of rubric management APIs for BITS DataScience Platform with bulk-update functionality", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Create Rubrics", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "[\n  {\n    \"project_id\": \"{{project_id}}\",\n    \"checkpoint_id\": \"{{checkpoint_id}}\",\n    \"title\": \"ML Model Evaluation Rubric\",\n    \"description\": \"Comprehensive rubric for evaluating machine learning models\",\n    \"criteria\": [\n      {\n        \"name\": \"Algorithm Selection\",\n        \"description\": \"Appropriate choice and justification of ML algorithm\",\n        \"points\": 25\n      },\n      {\n        \"name\": \"Data Preprocessing\",\n        \"description\": \"Quality of data cleaning and feature engineering\",\n        \"points\": 25\n      },\n      {\n        \"name\": \"Model Training\",\n        \"description\": \"Training methodology and hyperparameter tuning\",\n        \"points\": 25\n      },\n      {\n        \"name\": \"Evaluation & Validation\",\n        \"description\": \"Model performance metrics and validation techniques\",\n        \"points\": 25\n      }\n    ],\n    \"total_points\": 100,\n    \"is_template\": false,\n    \"metadata\": {\n      \"difficulty\": \"intermediate\",\n      \"estimated_time\": \"2 hours\"\n    }\n  }\n]"}, "url": {"raw": "{{base_url}}/api/rubrics", "host": ["{{base_url}}"], "path": ["api", "rubrics"]}}}, {"name": "Bulk Update Rubrics", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"project_id\": \"{{project_id}}\",\n  \"rubrics\": [\n    {\n      \"id\": \"{{rubric_id_1}}\",\n      \"checkpoint_id\": \"{{checkpoint_id_1}}\",\n      \"title\": \"Updated ML Model Rubric\",\n      \"description\": \"Enhanced machine learning evaluation criteria\",\n      \"criteria\": [\n        {\n          \"name\": \"Algorithm Selection\",\n          \"description\": \"Justification for chosen ML algorithm\",\n          \"points\": 25\n        },\n        {\n          \"name\": \"Data Preprocessing\",\n          \"description\": \"Feature engineering and data cleaning\",\n          \"points\": 25\n        },\n        {\n          \"name\": \"Model Training\",\n          \"description\": \"Training process and hyperparameter tuning\",\n          \"points\": 25\n        },\n        {\n          \"name\": \"Evaluation & Validation\",\n          \"description\": \"Model performance metrics and validation\",\n          \"points\": 25\n        }\n      ],\n      \"total_points\": 100,\n      \"is_template\": false\n    },\n    {\n      \"checkpoint_id\": \"{{checkpoint_id_2}}\",\n      \"title\": \"Deep Learning Project Rubric\",\n      \"description\": \"Assessment criteria for deep learning projects\",\n      \"criteria\": [\n        {\n          \"name\": \"Architecture Design\",\n          \"description\": \"Neural network architecture appropriateness\",\n          \"points\": 30\n        },\n        {\n          \"name\": \"Training Process\",\n          \"description\": \"Training methodology and optimization\",\n          \"points\": 35\n        },\n        {\n          \"name\": \"Performance Analysis\",\n          \"description\": \"Model evaluation and interpretation\",\n          \"points\": 35\n        }\n      ],\n      \"total_points\": 100,\n      \"is_template\": false\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/rubrics/bulk-update", "host": ["{{base_url}}"], "path": ["api", "rubrics", "bulk-update"]}}}, {"name": "Get All Rubrics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/rubrics?page=1&limit=20&sort_by=created_at&sort_order=DESC", "host": ["{{base_url}}"], "path": ["api", "rubrics"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "project_id", "value": "{{project_id}}", "disabled": true}, {"key": "is_template", "value": "false", "disabled": true}, {"key": "sort_by", "value": "created_at"}, {"key": "sort_order", "value": "DESC"}]}}}, {"name": "Get Rubric by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/rubrics/{{rubric_id}}", "host": ["{{base_url}}"], "path": ["api", "rubrics", "{{rubric_id}}"]}}}, {"name": "Update R<PERSON><PERSON>", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Updated Rubric Title\",\n  \"description\": \"Updated rubric description\",\n  \"criteria\": [\n    {\n      \"name\": \"Updated Criterion 1\",\n      \"description\": \"Updated description for criterion 1\",\n      \"points\": 30\n    },\n    {\n      \"name\": \"Updated Criterion 2\",\n      \"description\": \"Updated description for criterion 2\",\n      \"points\": 70\n    }\n  ],\n  \"total_points\": 100,\n  \"metadata\": {\n    \"updated_reason\": \"Improved clarity and scoring\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/rubrics/{{rubric_id}}", "host": ["{{base_url}}"], "path": ["api", "rubrics", "{{rubric_id}}"]}}}, {"name": "Delete Rubric", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/rubrics/{{rubric_id}}", "host": ["{{base_url}}"], "path": ["api", "rubrics", "{{rubric_id}}"]}}}, {"name": "Get R<PERSON><PERSON> Templates", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/rubrics/templates?page=1&limit=10", "host": ["{{base_url}}"], "path": ["api", "rubrics", "templates"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "template_name", "value": "ML", "disabled": true}]}}}, {"name": "Duplicate R<PERSON>ric <PERSON>late", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"project_id\": \"{{project_id}}\",\n  \"checkpoint_id\": \"{{checkpoint_id}}\",\n  \"title\": \"Duplicated ML Rubric\",\n  \"description\": \"<PERSON><PERSON><PERSON> duplicated from template for specific project\"\n}"}, "url": {"raw": "{{base_url}}/api/rubrics/{{template_rubric_id}}/duplicate", "host": ["{{base_url}}"], "path": ["api", "rubrics", "{{template_rubric_id}}", "duplicate"]}}}, {"name": "Get Project Rubrics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/rubrics/project/{{project_id}}", "host": ["{{base_url}}"], "path": ["api", "rubrics", "project", "{{project_id}}"]}}}, {"name": "Get Rubric Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/rubrics/stats?created_by={{user_id}}&is_template=false", "host": ["{{base_url}}"], "path": ["api", "rubrics", "stats"], "query": [{"key": "created_by", "value": "{{user_id}}"}, {"key": "is_template", "value": "false"}]}}}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:5001", "type": "string"}, {"key": "jwt_token", "value": "your_jwt_token_here", "type": "string"}, {"key": "project_id", "value": "your_project_uuid_here", "type": "string"}, {"key": "checkpoint_id", "value": "your_checkpoint_uuid_here", "type": "string"}, {"key": "checkpoint_id_1", "value": "your_first_checkpoint_uuid_here", "type": "string"}, {"key": "checkpoint_id_2", "value": "your_second_checkpoint_uuid_here", "type": "string"}, {"key": "rubric_id", "value": "your_rubric_uuid_here", "type": "string"}, {"key": "rubric_id_1", "value": "your_first_rubric_uuid_here", "type": "string"}, {"key": "template_rubric_id", "value": "your_template_rubric_uuid_here", "type": "string"}, {"key": "user_id", "value": "your_user_uuid_here", "type": "string"}]}