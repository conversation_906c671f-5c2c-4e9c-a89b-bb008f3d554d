{"info": {"name": "Project Templates API", "description": "Collection for BITS DataScience Platform - Project Template Management APIs", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:5001/api/projects", "type": "string"}, {"key": "jwt_token", "value": "", "type": "string"}, {"key": "project_id", "value": "", "type": "string"}, {"key": "template_id", "value": "", "type": "string"}, {"key": "course_id", "value": "", "type": "string"}], "item": [{"name": "Template Management", "item": [{"name": "Create Project Template", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"projectId\": \"{{project_id}}\",\n  \"templateName\": \"Data Analysis Template\",\n  \"templateDescription\": \"A comprehensive template for data analysis projects\",\n  \"category\": \"data-science\",\n  \"subcategory\": \"analysis\",\n  \"difficultyLevel\": \"intermediate\",\n  \"estimatedHours\": 40,\n  \"totalPoints\": 100,\n  \"learningObjectives\": [\n    \"Learn data cleaning techniques\",\n    \"Master statistical analysis\",\n    \"Create visualizations\"\n  ],\n  \"prerequisites\": [\n    \"Basic Python knowledge\",\n    \"Statistics fundamentals\"\n  ],\n  \"skillsCovered\": [\n    \"Python\",\n    \"Pandas\",\n    \"Matplotlib\",\n    \"Statistical Analysis\"\n  ],\n  \"technologiesUsed\": [\n    \"Python\",\n    \"Jupyter Notebook\",\n    \"Pandas\",\n    \"NumPy\",\n    \"Matplotlib\"\n  ],\n  \"tags\": [\n    \"data-analysis\",\n    \"python\",\n    \"statistics\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/templates", "host": ["{{base_url}}"], "path": ["templates"]}, "description": "Create a new project template from an existing project"}, "response": []}, {"name": "Get Project Templates", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/templates?page=1&limit=10&category=data-science&difficultyLevel=intermediate&isFeatured=true&isPublic=true&search=analysis", "host": ["{{base_url}}"], "path": ["templates"], "query": [{"key": "page", "value": "1", "description": "Page number (optional)"}, {"key": "limit", "value": "10", "description": "Items per page (optional, max 100)"}, {"key": "category", "value": "data-science", "description": "Filter by category (optional)"}, {"key": "subcategory", "value": "analysis", "description": "Filter by subcategory (optional)", "disabled": true}, {"key": "difficultyLevel", "value": "intermediate", "description": "Filter by difficulty: beginner, intermediate, advanced (optional)"}, {"key": "isFeatured", "value": "true", "description": "Filter featured templates (optional)"}, {"key": "isPublic", "value": "true", "description": "Filter public templates (optional)"}, {"key": "search", "value": "analysis", "description": "Search in template name/description (optional)"}]}, "description": "Get list of project templates with filtering options"}, "response": []}, {"name": "Duplicate Project from Template", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"courseId\": \"{{course_id}}\",\n  \"title\": \"My Data Analysis Project\",\n  \"description\": \"Custom project based on data analysis template\",\n  \"difficulty_level\": \"intermediate\",\n  \"estimatedHours\": 35,\n  \"totalPoints\": 90,\n  \"dueDate\": \"2024-12-31T23:59:59.000Z\",\n  \"startDate\": \"2024-01-01T00:00:00.000Z\",\n  \"learning_objectives\": [\n    \"Apply data cleaning techniques\",\n    \"Perform statistical analysis\",\n    \"Create meaningful visualizations\"\n  ],\n  \"prerequisites\": [\n    \"Python basics\",\n    \"Statistics knowledge\"\n  ],\n  \"instructions\": \"Follow the template guidelines and customize for your dataset\"\n}"}, "url": {"raw": "{{base_url}}/templates/{{template_id}}/duplicate", "host": ["{{base_url}}"], "path": ["templates", "{{template_id}}", "duplicate"]}, "description": "Create a new project by duplicating from a template"}, "response": []}, {"name": "Rate Project Template", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"rating\": 4.5\n}"}, "url": {"raw": "{{base_url}}/templates/{{template_id}}/rate", "host": ["{{base_url}}"], "path": ["templates", "{{template_id}}", "rate"]}, "description": "Rate a project template (0-5 stars)"}, "response": []}]}, {"name": "Project Creation with Templates", "item": [{"name": "Create Project as Template", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Machine Learning Template Project\",\n  \"description\": \"A comprehensive template for machine learning projects\",\n  \"courseId\": \"{{course_id}}\",\n  \"projectType\": \"individual\",\n  \"difficulty_level\": \"advanced\",\n  \"estimatedHours\": 60,\n  \"totalPoints\": 150,\n  \"dueDate\": \"2024-12-31T23:59:59.000Z\",\n  \"startDate\": \"2024-01-01T00:00:00.000Z\",\n  \"instructions\": \"Complete all machine learning pipeline steps\",\n  \"project_overview\": \"Build an end-to-end ML solution\",\n  \"learning_objectives\": [\n    \"Data preprocessing\",\n    \"Model selection\",\n    \"Model evaluation\",\n    \"Deployment strategies\"\n  ],\n  \"prerequisites\": [\n    \"Python programming\",\n    \"Statistics\",\n    \"Linear algebra\"\n  ],\n  \"tags\": [\n    \"machine-learning\",\n    \"python\",\n    \"scikit-learn\"\n  ],\n  \"isTemplate\": true,\n  \"templateCategory\": \"3c5b9d4e-6e4f-6f0f-3e3a-6a1aee3f9d66\",\n  \"templateSubcategory\": \"ml-template-subcategory-uuid\",\n  \"assignments\": [],\n  \"categoryId\": \"3c5b9d4e-6e4f-6f0f-3e3a-6a1aee3f9d66\",\n  \"instructorIds\": [],\n  \"teachingAssId\": [],\n  \"maxSubmissions\": 3,\n  \"lateSubmissionsAllowed\": true,\n  \"isScreen\": 1,\n  \"sandbox_time_duration\": \"2:00\",\n  \"late_submission_days_allowed\": 7\n}"}, "url": {"raw": "{{base_url}}/enhanced", "host": ["{{base_url}}"], "path": ["enhanced"]}, "description": "Create a new project that can be used as a template"}, "response": []}, {"name": "Create Project from Existing Template", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Student ML Project\",\n  \"description\": \"Individual machine learning project based on template\",\n  \"courseId\": \"{{course_id}}\",\n  \"projectType\": \"individual\",\n  \"difficulty_level\": \"advanced\",\n  \"estimatedHours\": 50,\n  \"totalPoints\": 120,\n  \"dueDate\": \"2024-11-30T23:59:59.000Z\",\n  \"startDate\": \"2024-10-01T00:00:00.000Z\",\n  \"template_id\": \"{{template_id}}\",\n  \"isTemplate\": false,\n  \"assignments\": [],\n  \"categoryId\": \"3c5b9d4e-6e4f-6f0f-3e3a-6a1aee3f9d66\",\n  \"instructorIds\": [\"instructor-uuid-1\", \"instructor-uuid-2\"],\n  \"teachingAssId\": [\"ta-uuid-1\"],\n  \"maxSubmissions\": 2,\n  \"lateSubmissionsAllowed\": false,\n  \"isScreen\": 1\n}"}, "url": {"raw": "{{base_url}}/enhanced", "host": ["{{base_url}}"], "path": ["enhanced"]}, "description": "Create a new project using an existing template"}, "response": []}]}, {"name": "Template Filtering & Search", "item": [{"name": "Get Templates by Category", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/templates?category=data-science&page=1&limit=20", "host": ["{{base_url}}"], "path": ["templates"], "query": [{"key": "category", "value": "data-science"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}, "description": "Get templates filtered by specific category"}, "response": []}, {"name": "Get Templates by <PERSON><PERSON><PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/templates?difficultyLevel=beginner&isPublic=true", "host": ["{{base_url}}"], "path": ["templates"], "query": [{"key": "difficultyLevel", "value": "beginner"}, {"key": "isPublic", "value": "true"}]}, "description": "Get beginner-level public templates"}, "response": []}, {"name": "Search Templates", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/templates?search=machine learning&isFeatured=true", "host": ["{{base_url}}"], "path": ["templates"], "query": [{"key": "search", "value": "machine learning"}, {"key": "isFeatured", "value": "true"}]}, "description": "Search for featured templates containing 'machine learning'"}, "response": []}, {"name": "Get Featured Templates", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/templates?isFeatured=true&isPublic=true&limit=5", "host": ["{{base_url}}"], "path": ["templates"], "query": [{"key": "isFeatured", "value": "true"}, {"key": "isPublic", "value": "true"}, {"key": "limit", "value": "5"}]}, "description": "Get top 5 featured public templates"}, "response": []}]}, {"name": "Template Usage Examples", "item": [{"name": "Data Science Template Example", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"projectId\": \"{{project_id}}\",\n  \"templateName\": \"Data Science Fundamentals\",\n  \"templateDescription\": \"Introduction to data science concepts and tools\",\n  \"category\": \"data-science\",\n  \"subcategory\": \"fundamentals\",\n  \"difficultyLevel\": \"beginner\",\n  \"estimatedHours\": 25,\n  \"totalPoints\": 75,\n  \"learningObjectives\": [\n    \"Understand data types and structures\",\n    \"Learn basic data manipulation\",\n    \"Create simple visualizations\",\n    \"Perform descriptive statistics\"\n  ],\n  \"prerequisites\": [\n    \"Basic programming knowledge\",\n    \"High school mathematics\"\n  ],\n  \"skillsCovered\": [\n    \"Python basics\",\n    \"Pandas fundamentals\",\n    \"Data visualization\",\n    \"Descriptive statistics\"\n  ],\n  \"technologiesUsed\": [\n    \"Python\",\n    \"Pandas\",\n    \"Matplotlib\",\n    \"Jupyter Notebook\"\n  ],\n  \"tags\": [\n    \"beginner\",\n    \"data-science\",\n    \"python\",\n    \"fundamentals\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/templates", "host": ["{{base_url}}"], "path": ["templates"]}, "description": "Create a beginner-friendly data science template"}, "response": []}, {"name": "Machine Learning Template Example", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"projectId\": \"{{project_id}}\",\n  \"templateName\": \"Advanced Machine Learning Pipeline\",\n  \"templateDescription\": \"Complete ML pipeline from data preprocessing to model deployment\",\n  \"category\": \"machine-learning\",\n  \"subcategory\": \"supervised-learning\",\n  \"difficultyLevel\": \"advanced\",\n  \"estimatedHours\": 80,\n  \"totalPoints\": 200,\n  \"learningObjectives\": [\n    \"Master data preprocessing techniques\",\n    \"Implement various ML algorithms\",\n    \"Perform hyperparameter tuning\",\n    \"Deploy models to production\",\n    \"Evaluate model performance\"\n  ],\n  \"prerequisites\": [\n    \"Strong Python programming\",\n    \"Statistics and probability\",\n    \"Linear algebra\",\n    \"Calculus basics\"\n  ],\n  \"skillsCovered\": [\n    \"Feature engineering\",\n    \"Model selection\",\n    \"Cross-validation\",\n    \"Ensemble methods\",\n    \"Model deployment\"\n  ],\n  \"technologiesUsed\": [\n    \"Python\",\n    \"Scikit-learn\",\n    \"TensorFlow\",\n    \"Docker\",\n    \"Flask/FastAPI\"\n  ],\n  \"tags\": [\n    \"advanced\",\n    \"machine-learning\",\n    \"deployment\",\n    \"production\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/templates", "host": ["{{base_url}}"], "path": ["templates"]}, "description": "Create an advanced machine learning template"}, "response": []}, {"name": "Web Development Template Example", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"projectId\": \"{{project_id}}\",\n  \"templateName\": \"Full-Stack Web Application\",\n  \"templateDescription\": \"Build a complete web application with frontend and backend\",\n  \"category\": \"web-development\",\n  \"subcategory\": \"full-stack\",\n  \"difficultyLevel\": \"intermediate\",\n  \"estimatedHours\": 60,\n  \"totalPoints\": 140,\n  \"learningObjectives\": [\n    \"Design responsive user interfaces\",\n    \"Implement RESTful APIs\",\n    \"Manage application state\",\n    \"Handle user authentication\",\n    \"Deploy web applications\"\n  ],\n  \"prerequisites\": [\n    \"HTML/CSS fundamentals\",\n    \"JavaScript programming\",\n    \"Basic database concepts\"\n  ],\n  \"skillsCovered\": [\n    \"React.js\",\n    \"Node.js\",\n    \"Express.js\",\n    \"Database design\",\n    \"API development\"\n  ],\n  \"technologiesUsed\": [\n    \"React\",\n    \"Node.js\",\n    \"Express\",\n    \"MongoDB\",\n    \"JWT Authentication\"\n  ],\n  \"tags\": [\n    \"web-development\",\n    \"full-stack\",\n    \"react\",\n    \"nodejs\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/templates", "host": ["{{base_url}}"], "path": ["templates"]}, "description": "Create a full-stack web development template"}, "response": []}]}, {"name": "Template Rating & Feedback", "item": [{"name": "Rate Template - Excellent (5 stars)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"rating\": 5.0\n}"}, "url": {"raw": "{{base_url}}/templates/{{template_id}}/rate", "host": ["{{base_url}}"], "path": ["templates", "{{template_id}}", "rate"]}, "description": "Give a 5-star rating to a template"}, "response": []}, {"name": "Rate Template - Good (4 stars)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"rating\": 4.0\n}"}, "url": {"raw": "{{base_url}}/templates/{{template_id}}/rate", "host": ["{{base_url}}"], "path": ["templates", "{{template_id}}", "rate"]}, "description": "Give a 4-star rating to a template"}, "response": []}, {"name": "Rate Template - Average (3 stars)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"rating\": 3.0\n}"}, "url": {"raw": "{{base_url}}/templates/{{template_id}}/rate", "host": ["{{base_url}}"], "path": ["templates", "{{template_id}}", "rate"]}, "description": "Give a 3-star rating to a template"}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-set common variables if not already set", "if (!pm.collectionVariables.get('base_url')) {", "    pm.collectionVariables.set('base_url', 'http://localhost:5001/api/projects');", "}", "", "// Log current request for debugging", "console.log('Making request to:', pm.request.url.toString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Common test for all requests", "pm.test('Response time is less than 5000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "// Auto-extract IDs from successful responses", "if (pm.response.code === 200 || pm.response.code === 201) {", "    try {", "        const responseJson = pm.response.json();", "        ", "        // Extract project ID if present", "        if (responseJson.id) {", "            pm.collectionVariables.set('project_id', responseJson.id);", "        }", "        ", "        // Extract template ID if present", "        if (responseJson.template && responseJson.template.id) {", "            pm.collectionVariables.set('template_id', responseJson.template.id);", "        }", "        ", "        // Extract course ID if present", "        if (responseJson.courseId) {", "            pm.collectionVariables.set('course_id', responseJson.courseId);", "        }", "    } catch (e) {", "        // Ignore JSON parsing errors", "    }", "}"]}}]}